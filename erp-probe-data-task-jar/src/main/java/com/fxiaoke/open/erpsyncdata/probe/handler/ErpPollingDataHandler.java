package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.CheckProductErpDataMonitor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import com.fxiaoke.open.erpsyncdata.probe.service.RetryFailDataService;
import com.google.common.collect.ImmutableSet;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:10 2022/6/9
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "erpPollingDataHandler")
public class ErpPollingDataHandler  extends IJobHandler {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ProbeDataTaskService probeDataTaskService;
    @Autowired
    private CheckProductErpDataMonitor checkProductErpDataMonitor;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private RetryFailDataService retryFailDataService;


    //触发次数计数
    private static long cntJobTriggerNumber = 0;
    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }
    public void executeJob(TriggerParam triggerParam) {
        log.info("execute job,triggerParam:{}, cntJobTriggerNumber:{}", triggerParam, cntJobTriggerNumber++);

//        //运行在本作业服务器的分片序列号
//        int shardingItem = triggerParam.getBroadcastIndex();
//        //分片总数
//        int shardingTotalCount = triggerParam.getBroadcastTotal();
        //只处理有开启快照的企业
        List<String> allTenantIds = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listEnableSnapshotTenantId();
        List<String> tenantIds=allTenantIds.stream().distinct().collect(Collectors.toList());//去重
//        //筛选出匹配分片的企业
//        tenantIds.removeIf(ei -> !matchSharding(shardingItem, shardingTotalCount, ei));
        ImmutableSet<String> whiteList = tenantConfigurationManager.getWhiteList(TenantConfigurationTypeEnum.NOT_QUERY_ERP_DATA_TENANTS);
        if(CollectionUtils.isNotEmpty(whiteList)){
            //暂时不轮询企业
            tenantIds.removeAll(whiteList);
        }
        //后台任务，最大支持200个企业,调用erp接口获取数据
        ParallelUtils.ParallelTask backgroundTask = ParallelUtils.createErpRollingDataTask();
        for (String tenantId : tenantIds) {
            //每个企业在单独线程运行
            backgroundTask.submit(()-> probeDataTaskService.executeProbeErpDataJob(tenantId, null));
        }
        backgroundTask.run();
        //后台任务，最大支持200个企业，历史数据同步
        ParallelUtils.ParallelTask rollingErpHistoryDataTask = ParallelUtils.createRollingErpHistoryData();
        for (String tenantId : tenantIds) {
            rollingErpHistoryDataTask.submit(()-> {
//                重试放第一个,防止限额导致的没有同步
                TraceUtil.initTraceWithFormat(tenantId);
                probeDataTaskService.retryPollingTempFail(tenantId);
                probeDataTaskService.executeRollingErpHistoryDataJob(tenantId);
                checkProductErpDataMonitor.checkInit(tenantId);
            });
        }
        rollingErpHistoryDataTask.run();

        //临时库监控, 5次调度执行一次。
        if ((cntJobTriggerNumber % 5) == 0) {
            checkProductErpDataMonitor.checkErpDataProduct();
        }
        //触发mq失败的数据
        retryFailDataService.retryDataServiceHandler();
    }

    private boolean matchSharding(int index, int total, String tenantId) {
        long longId = getLong(tenantId);
        return longId % total == index;
    }

    private long getLong(String tenantId) {
        long longId;
        try {
            longId = Long.parseLong(tenantId);
        } catch (NumberFormatException e) {
            log.warn("this tenantId is not long type,tenantId:{}", tenantId, e);
            longId = tenantId.hashCode();
        }
        return longId;
    }
}
