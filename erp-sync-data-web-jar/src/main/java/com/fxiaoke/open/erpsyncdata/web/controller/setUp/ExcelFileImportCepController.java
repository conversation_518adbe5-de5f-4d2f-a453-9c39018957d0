package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.model.ImportIntegrationStreamMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.AsyncImportExcelData;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.AsyncImportObjectDataMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.remote.StoneFileManager;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.io.InputStream;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 * @date 2024/6/19
 */
@Slf4j
@Api(tags = "通用excel文件导入接口")
@RestController()
@RequestMapping("cep/setUp/excelFile")
public class ExcelFileImportCepController extends AsyncSupportController {
    @Autowired
    private FileService fileService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private StoneFileManager stoneFileManager;

    @ApiOperation(value = "导入集成流数据")
    @RequestMapping(value = "/importIntegrationStreamData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportIntegrationStreamMapping.Result>> importIntegrationStreamData(@RequestBody ImportIntegrationStreamMapping.Arg arg,
                                                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer loginUserId = getLoginUserId();
        String dataCenterId = getDcId();

        arg.setTenantId(tenantId);
        arg.setUserId(loginUserId);

        return asyncExecute(() -> {
            try {
                if(StringUtils.isEmpty(tenantId) || loginUserId==null || StringUtils.isEmpty(dataCenterId) || StringUtils.isEmpty(arg.getDataCenterId())) {
                    return Result.newError(i18NStringManager.get(I18NStringEnum.s2072, lang, tenantId));
                }

                return fileService.batchImportErpIntegrationStreams(arg, lang);
            } catch (Exception e) {
                log.warn("ExcelFileImportCepController.importIntegrationStreamData,exception={}", e.getMessage());
                return Result.newError(i18NStringManager.get(I18NStringEnum.s2072, lang, tenantId) + e.getMessage());
            }
        }, 5, false, i18NStringManager.get(I18NStringEnum.s4039, lang, tenantId), r -> r.isSuccess() ? r.getData().getSheetResults().stream().map(sheetResult -> sheetResult.getSheetName() + i18NStringManager.get2(I18NStringEnum.s225, lang, tenantId, sheetResult.getResult().getPrintMsg())).collect(Collectors.joining("\n")) : null, Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    @ApiOperation(value = "导入对象数据映射，异步请求处理")
    @PostMapping( "/importObjectDataMapping")
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportObjectDataMapping(@RequestBody AsyncImportObjectDataMapping.Arg arg) {
        String tenantId = getLoginUserTenantId();
        final String lang = getLang();
        final Integer userId = getLoginUserId();

        return asyncExecute(() -> {
            try (InputStream inputStream = stoneFileManager.downloadByPath(tenantId, arg.getNpath(), arg.getFileType())) {
                return fileService.importObjectDataMapping(tenantId, userId, arg.getPloyDetailId(), arg.getExcelType(), inputStream, arg.getFileType(), lang);
            } catch (Exception e) {
                log.warn("ExcelFileController.importObjectFieldData,exception={}", e.getMessage());
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getMessage());
            }
        }, 10, false, I18NStringEnum.s3622, lang);
    }

    @ApiOperation(value = "导入数据")
    @PostMapping("/importExcelData")
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportExcelData(@RequestBody AsyncImportExcelData.Arg arg) throws IOException {

        String tenantId = getLoginUserTenantId();
        final String lang = getLang();
        final String dcId = getDcId();
        final Integer userId = getLoginUserId();

        return asyncExecute(() -> {
            ImportExcelFile.FieldDataMappingArg importArg = new ImportExcelFile.FieldDataMappingArg();
            importArg.setDataCenterId(dcId);
            importArg.setDataType(arg.getDataType());
            importArg.setTenantId(tenantId);
            importArg.setUserId(userId);
            importArg.setExcelType(arg.getExcelType()); //  OBJ_DATA_MAPPING
            importArg.setLang(lang);
            importArg.setFileType(arg.getFileType());

            try (InputStream inputStream = stoneFileManager.downloadByPath(tenantId, arg.getNpath(), arg.getFileType())) {
                importArg.setFileStream(inputStream);
                return fileService.importExcelFile(importArg, dcId, lang);
            } catch (Exception e) {
                log.warn("ExcelFileController.importObjectFieldData,exception={}", e.getMessage());
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getMessage());
            }
        }, 10, false, I18NStringEnum.s3622, lang);
    }
}
