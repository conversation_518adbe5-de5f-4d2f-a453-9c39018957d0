package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.arg.MigrateProductCateArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.SwitchStreamStatusManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.MigratePloyService;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.ConnectInfoServiceImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandlerImpl.SourceObjSalesOrderHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.writer.manager.CrmCycleSyncCheckManager;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 策略相关管理接口
 * @IgnoreI18n
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/1/11
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
@Slf4j
public class SuperAdminPloyController extends SuperAdminBaseController {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private MigratePloyService migratePloyService;
    @Autowired
    private CrmCycleSyncCheckManager crmCycleSyncCheckManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SwitchStreamStatusManager switchStreamStatusManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ConnectInfoServiceImpl connectInfoServiceImpl;
    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private SourceObjSalesOrderHandler sourceObjSalesOrderHandler;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @PostMapping("initProductCatePloy")
    public Result<String> initProductCatePloy(@RequestBody List<String> tenantIds,
                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        for (String tenantId : tenantIds) {
            initProductCateSingle(tenantId, lang);
        }
        return Result.newSuccess();
    }

    public void initProductCateSingle(String tenantId, String lang) {
        SendMsgHelper msgHelper = initSendMsgHelper(tenantId, i18NStringManager.get(I18NStringEnum.s808, lang, tenantId));
        List<ErpConnectInfoEntity> connectInfos = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listErpDcByTenantId(tenantId);
        connectInfos.removeIf(v -> !v.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD));
        if (connectInfos.isEmpty()) {
            msgHelper.append(i18NStringManager.get(I18NStringEnum.s809, lang, tenantId))
                    .sendMsg(AlarmRuleType.OTHER,
                            AlarmRuleType.OTHER.getName(i18NStringManager, null, tenantId),
                            AlarmType.OTHER,
                            AlarmLevel.GENERAL);
            return;
        }
        for (ErpConnectInfoEntity connectInfo : connectInfos) {
            MigrateProductCateArg migrateProductCateArg = new MigrateProductCateArg();
            migrateProductCateArg.setTenantId(tenantId);
            migrateProductCateArg.setDcId(connectInfo.getId());
            migrateProductCateArg.setErpProductCateObjApiName(K3CloudForm.SAL_MATERIALGROUP);
            Result<String> stringResult = migratePloyService.migrateProductCate(migrateProductCateArg, lang);
            msgHelper.append(stringResult);
        }
        msgHelper.append(";").append(i18NStringManager.get(I18NStringEnum.s810, lang, tenantId));
    }

    @PostMapping("migrateProductCate")
    public Result<String> migrateProductCate(@RequestBody MigrateProductCateArg migrateProductCateArg,
                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        Result<String> stringResult = migratePloyService.migrateProductCate(migrateProductCateArg, lang);
        return stringResult;
    }

    @GetMapping("breakPloy/{tenantId}/{snapId}/{objApiName}")
    public Result<String> breakPloy(@PathVariable String tenantId, @PathVariable String snapId, @PathVariable String objApiName) {
        crmCycleSyncCheckManager.breakPloy(tenantId, snapId, objApiName);
        return Result.newSuccess();
    }


    /**
     * 重刷某段时间的数据
     */
    @GetMapping("processChangeDataByTime")
    public DeferredResult<Result<Map<String, Map<String, String>>>> processChangeDataByTime(@RequestParam() String tenantIds,
                                                                        @RequestParam() Long startTime,
                                                                        @RequestParam() Long endTime,
                                                                        @RequestParam(required = false) Boolean ignoreErp,
                                                                        @RequestParam(required = false) Boolean ignoreCrm,
                                                                        @RequestParam(required = false) Boolean resetAllErp) {
        return asyncExecute(() -> {
            Map<String, String> fails = new HashMap<>();
            log.info("processChangeDataByTime start tenantId:{}", tenantIds);
            Splitter.on(",").splitToList(tenantIds).forEach(tenantId -> {
                try {
                    final List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantIdAndStatus(tenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
                    Long l = 0L;
                    if (BooleanUtils.isNotTrue(ignoreErp)) {
                        final List<String> apiNames;
                        if (BooleanUtils.isTrue(resetAllErp)) {
                            // 部分企业的集成流有问题,找不到对应的真实对象apiName,更新所有对象,有可能会超时
                            apiNames = null;
                        } else {
                            apiNames = syncPloyDetailEntities.stream()
                                    .filter(entity -> Objects.equals(entity.getSourceTenantType(), TenantTypeEnum.ERP.getType()))
                                    .map(SyncPloyDetailEntity::getSourceObjectApiName)
                                    // 集成流上是中间对象,需转为实际对象
                                    .map(splitName -> erpObjManager.getRealObjApiName(tenantId, splitName))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                        }
                        l = erpTempDataDao.batchUpdateLastSyncTimeByTime(tenantId, apiNames, startTime, endTime);
                        if (l > 0) {
                            log.info("processChangeDataByTime erp change tenantId:{} count:{}", tenantId, l);
                        }
                    }
                    AtomicInteger changeCount = new AtomicInteger(l.intValue());

                    if (BooleanUtils.isNotTrue(ignoreCrm)) {
                        final Map<String, List<SyncPloyDetailEntity>> collect = syncPloyDetailEntities.stream()
                                .filter(entity -> Objects.equals(entity.getSourceTenantType(), TenantTypeEnum.CRM.getType()))
                                .collect(Collectors.groupingBy(SyncPloyDetailEntity::getSourceObjectApiName));
                        collect.forEach((sourceObjApiName, ployDetails) -> {
                            Set<String> updateIds = new HashSet<>();
                            Set<String> inactiveIds = new HashSet<>();
                            ployDetails.forEach(ployDetail -> {
                                SyncPloyDetailResult ployDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultByEntity(i18NStringManager, "zh-CN", tenantId, ployDetail);
                                final Pair<Set<String>, Set<String>> setSetPair = switchStreamStatusManager.processEnableCrm2Erp(tenantId, ployDetailResult, sourceObjApiName, startTime, endTime);
                                updateIds.addAll(setSetPair.getLeft());
                                inactiveIds.addAll(setSetPair.getRight());
                            });

                            if (CollectionUtils.isNotEmpty(updateIds) || CollectionUtils.isNotEmpty(inactiveIds)) {
                                log.info("processChangeDataByTime crm change tenantId:{} apiName:{} updateIds:{} InvalidIds:{}", tenantId, sourceObjApiName, updateIds, inactiveIds);
                            }

                            changeCount.addAndGet(updateIds.size() + inactiveIds.size());
                        });
                    }

                    log.info("processChangeDataByTime success tenantId:{} update:{}", tenantId, changeCount.get());
                } catch (Exception e) {
                    log.error("processChangeDataByTime error, tenantId:{}", tenantId, e);
                    fails.put(tenantId, e.getMessage());
                }
            });
            log.info("processChangeDataByTime end tenantId:{}", tenantIds);

            if (MapUtils.isNotEmpty(fails)) {
                log.warn("processChangeDataByTime had fail,startTime:{} endTime:{}  tenantIds:{}", startTime, endTime, fails.keySet());
            }

            return Result.newSuccess(ImmutableMap.of("data", fails));
        }, 10, true, "processChangeDataByTime", "zh-CN");
    }

    @GetMapping("getAllSapCpqPloyDetails")
    public DeferredResult<Result<List<SyncPloyDetailEntity>>> getAllSapCpqPloyDetails(@RequestParam() String tenantIds, @RequestParam(required = false) Boolean checkStd, @RequestParam(required = false) Boolean checkSap, @RequestParam(required = false) Boolean erp2Crm, @RequestParam(required = false) Boolean crm2Erp, @RequestParam(required = false) Integer status) {
        return asyncExecute(() -> {
            if (BooleanUtils.isNotTrue(checkSap) && BooleanUtils.isNotTrue(checkStd)) {
                return Result.newError("标准连接器和Sap连接器最少需要勾选一个");
            }
            if (BooleanUtils.isNotTrue(erp2Crm) && BooleanUtils.isNotTrue(crm2Erp)) {
                return Result.newError("集成流方向最少需要勾选一个");
            }

            List<String> checkTenantIds;
            if (StringUtils.isBlank(tenantIds)) {
                checkTenantIds = erpConnectInfoDao.listTenantId();
            } else {
                checkTenantIds = Splitter.on(",").splitToList(tenantIds);
            }

            final List<SyncPloyDetailEntity> collect = checkTenantIds.parallelStream()
                    // 不查代管下游企业
                    .filter(tenantId -> StringUtils.isNotBlank(tenantId) && !configCenterConfig.isManagedEnterprise(tenantId, false))
                    .flatMap(tenantId -> {
                        final List<SyncPloyDetailEntity> erp2CrmCpqPloyDetails =
                                BooleanUtils.isTrue(erp2Crm) ?
                                        syncCpqService.getAllSapErp2CrmCpqPloyDetails(tenantId) :
                                        new ArrayList<>();
                        final List<SyncPloyDetailEntity> crm2ErpCpqPloyDetails =
                                BooleanUtils.isTrue(crm2Erp) ?
                                        sourceObjSalesOrderHandler.getAllSapCrm2ErpCpqPloyDetails(tenantId) :
                                        new ArrayList<>();
                        return Stream.concat(
                                erp2CrmCpqPloyDetails.stream(),
                                crm2ErpCpqPloyDetails.stream()
                        );
                    })
                    .filter(ployDetailEntity -> Objects.isNull(status) || status <= 0 || Objects.equals(status, ployDetailEntity.getStatus()))
                    .filter(ployDetailEntity -> {
                        if (BooleanUtils.isTrue(checkSap) && BooleanUtils.isTrue(checkStd)) {
                            return true;
                        }
                        String erpDcId = Objects.equals(ployDetailEntity.getSourceTenantType(), TenantType.ERP) ?
                                ployDetailEntity.getSourceDataCenterId() :
                                ployDetailEntity.getDestDataCenterId();
                        ErpChannelEnum channel = BooleanUtils.isTrue(checkSap) ? ErpChannelEnum.ERP_SAP : ErpChannelEnum.STANDARD_CHANNEL;
                        final ErpConnectInfoEntity byIdAndTenantId = erpConnectInfoManager.getByIdAndTenantId(ployDetailEntity.getTenantId(), erpDcId);
                        return Objects.nonNull(byIdAndTenantId) && Objects.equals(channel, byIdAndTenantId.getChannel());
                    })
                    // 最多搜集1000集成流
                    .limit(1000)
                    .collect(Collectors.toList());

            final Map<String, List<String>> ployDetailIdMap = collect.stream()
                    .collect(Collectors.groupingBy(
                            SyncPloyDetailEntity::getTenantId,
                            Collectors.mapping(SyncPloyDetailEntity::getId, Collectors.toList())
                    ));
            log.info("getAllSapCpqPloyDetails Top1000 result:{}", ployDetailIdMap);

            return Result.newSuccess(collect);
        }, 300, true, "getAllSapCpqPloyDetails", "zh-CN");
    }
}
