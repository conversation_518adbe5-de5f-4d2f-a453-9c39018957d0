package com.fxiaoke.open.erpsyncdata.admin.arg;


import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncPloyUpdateSyncConditionsArg implements Serializable {
    private String id;
    @ApiModelProperty("1 erp->crm 2 crm->rep ")
    private Integer type;
    @ApiModelProperty("对接企业")
    private Integer relationTenantId;
    private SyncConditionsData syncConditions;
    private List<SyncConditionsData> detailObjectSyncConditions;
}
