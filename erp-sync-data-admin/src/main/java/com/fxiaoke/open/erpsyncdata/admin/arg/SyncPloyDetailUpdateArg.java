package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailUpdateArg implements Serializable {
    @ApiModelProperty("策略明细id")
    private String id;
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("源企业id")
    private List<String> sourceTenantIds;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标企业类型")
    private Integer destTenantType;
    @ApiModelProperty("目标企业id")
    private List<String> destTenantIds;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
}
