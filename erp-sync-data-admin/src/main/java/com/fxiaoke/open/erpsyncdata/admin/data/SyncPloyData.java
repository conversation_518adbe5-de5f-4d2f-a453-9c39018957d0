package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncPloyData extends BaseData implements Serializable {
    private String id;
    private Integer type;
    private Integer appType;
    private String tenantId;
    private String name;
    private String objectApiName;
    private List<String> detailObjectApiNames;
    private String remark;
}
