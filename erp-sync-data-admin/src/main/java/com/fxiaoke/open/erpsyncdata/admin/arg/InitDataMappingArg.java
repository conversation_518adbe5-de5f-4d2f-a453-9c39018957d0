package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class InitDataMappingArg implements Serializable {
    @ApiModelProperty("策略明细id")
    private String ployDetailId;
    @ApiModelProperty("主对象映射")
    private ObjectFieldKeyArg masterObjectMapping;
    @ApiModelProperty("从对象映射")
    private List<ObjectFieldKeyArg> detailObjectMappings;

    @Data
    @ApiModel
    public static class ObjectFieldKeyArg implements Serializable {
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("源对象主键字段Key")
        private List<String> sourceObjectIdFieldKey;
        @ApiModelProperty("目标对象主键字段Key")
        private List<String> destObjectIdFieldKey;
        @ApiModelProperty("源对象Name字段Key")
        private List<String> sourceObjectNameFieldKey;
        @ApiModelProperty("目标对象Name字段Key")
        private List<String> destObjectNameFieldKey;
    }
}
