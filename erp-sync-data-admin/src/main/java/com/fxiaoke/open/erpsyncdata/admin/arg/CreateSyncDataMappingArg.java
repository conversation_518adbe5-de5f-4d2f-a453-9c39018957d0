package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 16:47 2021/1/19
 * @Desc:
 */
@Data
@ApiModel
public class CreateSyncDataMappingArg extends SyncDataMappingResult {
    private static final long serialVersionUID = 2387149185579373235L;

    @ApiModelProperty("策略明细id")
    private String ployDetailId;

    @ApiModelProperty("允许修改源数据id，默认值：false")
    private boolean enableUpdateSourceDataId = false;
}
