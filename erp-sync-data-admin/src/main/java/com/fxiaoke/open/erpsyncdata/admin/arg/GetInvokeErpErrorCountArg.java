package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class GetInvokeErpErrorCountArg implements Serializable {
    @ApiModelProperty("数据中心id")
    private String dcId;
    @ApiModelProperty("erp对象")
    private List<String> erpObjApiName;
    @ApiModelProperty("开始统计时间")
    private Long startTime;
}
