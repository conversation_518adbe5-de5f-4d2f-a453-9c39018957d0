package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:13 2021/7/5
 * @Desc:
 */
@Data
public class DeleteSyncDataAndSyncDataMappingArg implements Serializable {
    //企业id
    @ApiModelProperty("企业id")
    private String tenantId;
    //是否通过企业id删除所有SyncDataAndSyncDataMapping
    @ApiModelProperty("是否通过企业id删除所有")
    private Boolean deleteAll=false;
    //源对象apiName
    @ApiModelProperty("源对象apiName")
    private String sourceObjApiName;
    //目标对象apiName
    @ApiModelProperty("目标对象apiName")
    private String destObjApiName;
    //策略明细
    @ApiModelProperty("策略明细")
    private String ployDetailId;
}
