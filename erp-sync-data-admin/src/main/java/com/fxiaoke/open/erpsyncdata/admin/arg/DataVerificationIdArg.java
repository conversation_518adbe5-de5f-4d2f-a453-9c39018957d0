package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:18 2022/11/3
 * @Desc:
 */
@Data
@ApiModel
public class DataVerificationIdArg implements Serializable {
    @ApiModelProperty(value = "id")
    @JSONField(serialize = false)
    private String dataVerificationTaskId;
    @ApiModelProperty(value = "集成流id")
    @JSONField(serialize = false)
    private String streamId;
    @ApiModelProperty(value = "任务id")
    @JSONField(serialize = false)
    private String taskId;
    @ApiModelProperty(value = "id列表")
    @JSONField(serialize = false)
    private List<String> idList;
    @ApiModelProperty(value = "文件url")
    @JSONField(serialize = false)
    private String fileUrl;
}
