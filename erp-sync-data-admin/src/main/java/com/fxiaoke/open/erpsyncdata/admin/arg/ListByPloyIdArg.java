package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ListByPloyIdArg implements Serializable {
    @ApiModelProperty("策略id")
    private String ployId;
//    @ApiModelProperty("按启用状态筛选")
//    private Integer status;
//    @ApiModelProperty("搜索内容")
//    private String searchText;
    @ApiModelProperty("页码")
    private Integer pageNumber = 1;
    @ApiModelProperty("每页大小")
    private Integer pageSize = 20;
}
