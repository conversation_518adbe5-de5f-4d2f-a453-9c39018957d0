package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel
public class InitDataCenterInfoArg extends CepArg{
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel;
    @ApiModelProperty("数据中心名称")
    public String dataCenterName;
    @ApiModelProperty("默认是channel，aplClassName有值时是aplClassName")
    public String connectorKey;
}
