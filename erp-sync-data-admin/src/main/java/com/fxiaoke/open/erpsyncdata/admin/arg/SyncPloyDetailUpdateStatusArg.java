package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailUpdateStatusArg implements Serializable {
    @ApiModelProperty("策略明细id")
    private String id;
    @ApiModelProperty("策略明细状态 1启用，2停用")
    private Integer status;
    @ApiModelProperty("需要同步停用期间的数据")
    private boolean needSyncDuringStop;
    @ApiModelProperty("数据中心id，inner接口需要在这传，前端接口不传输")
    private String dcId;

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    @ApiModel
    public static class BatchArg extends SyncPloyDetailUpdateStatusArg {

        @ApiModelProperty("选中的集成流列表")
        private List<String> ids;

        @ApiModelProperty("对所有集成流执行,为true时，ids参数无效")
        private boolean updateAllStreams;
    }
}
