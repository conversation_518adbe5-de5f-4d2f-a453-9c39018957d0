package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class GetFiledApiNameVariableValueListArg implements Serializable {
    @ApiModelProperty("下发/采集类型  1 erp->crm 2 crm->rep")
    /**
     * {@link SyncPloyTypeEnum}
     */
    private Integer type;
    @ApiModelProperty("目標企業")
    private List<Integer> destTenantIds;

}
