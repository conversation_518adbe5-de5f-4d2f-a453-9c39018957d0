package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailCreateArg implements Serializable {
    @ApiModelProperty("dcId")
    private String dcId;
    @ApiModelProperty("策略id")
    private String ployId;
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("源企业id")
    private List<String> sourceTenantIds;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标企业类型")
    private Integer destTenantType;
    @ApiModelProperty("目标企业id")
    private List<String> destTenantIds;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("从对象映射")
    private List<DetailObjectMappingCreateArg> detailObjectMappings = new ArrayList<>();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailObjectMappingCreateArg {
        private String sourceObjectApiName;
        private String destObjectApiName;
    }
}
