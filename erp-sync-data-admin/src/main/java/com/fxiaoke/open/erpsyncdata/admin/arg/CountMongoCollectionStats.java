package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface CountMongoCollectionStats {

    @Data
    class Arg implements Serializable {
        /**
         * 租户Id
         */
        private List<String> tenantIds;

        /**
         * 所有日志表
         */
        private Boolean allLog;

        /**
         * 临时表
         */
        private Boolean temp;
    }

    @Data
    class TenantCollStat extends CollStat implements Serializable {
        /**
         * 启动租户Id
         */
        private String tenantId;
    }
}
