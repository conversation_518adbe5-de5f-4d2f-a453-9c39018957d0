package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.PollingIntervalUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TriggerConfig;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 16:22 2022/2/16
 * @Desc:
 */
@Data
@ApiModel
@Slf4j
public class SyncRulesWebData implements Serializable {
    @ApiModelProperty("同步方式,轮询：get,推送：push，默认是轮询")
    @Deprecated
    private String syncType = SyncTypeEnum.get.name();//默认是轮询
    @ApiModelProperty("同步方式,轮询：get,推送：push，默认是轮询")
    private List<String> syncTypeList = Lists.newArrayList();
    @ApiModelProperty("事件类型， 1新增 2修改 3作废 4依赖处理")
    private Set<Integer> events;
    @ApiModelProperty("轮询时间间隔")
    private PollingIntervalApiDto pollingInterval;
    @ApiModelProperty("erp接口信息")
    private List<ApiMessageData> apiMsgList;
    @ApiModelProperty("crm接口信息")
    private List<ApiMessageData> crmApiMsgList;
    /**
     * 是否跟随主对象同步。孙表无法配置同步方式和接口，跟随主对象。
     */
    private boolean syncFollowMain = false;
    /**
     * 主对象名称 当{@link SyncRulesWebData#syncFollowMain}为true时有值
     */
    private String mainObjectName;
    @ApiModelProperty("触发字段配置")
    private TriggerConfig triggerConfig;


    public void setPollingInterval(String tenantId, PollingIntervalApiDto pollingInterval, int cronBeginMinute) {
        this.pollingInterval = pollingInterval;
        //生成cron储存
        PollingIntervalUtil.fillCron(tenantId,pollingInterval, cronBeginMinute);
    }

    public void syncFollowMain(String mainObjectName){
        this.syncFollowMain = true;
        this.mainObjectName = mainObjectName;
    }

}
