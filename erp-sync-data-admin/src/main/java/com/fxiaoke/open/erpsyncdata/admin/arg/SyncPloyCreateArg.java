package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyCreateArg implements Serializable {
    @ApiModelProperty("1 erp->crm 2 crm->rep")
    private Integer type;
    @ApiModelProperty("数据来源哪个应用, 第一期创建时前端写死为CRM即可")
    private String appId;
    @ApiModelProperty("策略名称")
    private String name;
    @ApiModelProperty("主对象apiName")
    private String objectApiName;
    @ApiModelProperty("从对象apiName")
    private List<String> detailObjectApiNames;
    @ApiModelProperty("备注")
    private String remark;
}
