package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface EnableStreamByTenant {

    @Data
    class Arg implements Serializable {
        /**
         * 启动租户Id
         */
        private List<String> tenantIds;

        /**
         * 启动通知企业Id
         */
        private String notifyTenantId;

        /**
         * 启动通知员工Id
         */
        private List<Integer> notifyEmployeeIds;

        /**
         * 上游企业Id
         */
        private String upStreamId;
    }



}
