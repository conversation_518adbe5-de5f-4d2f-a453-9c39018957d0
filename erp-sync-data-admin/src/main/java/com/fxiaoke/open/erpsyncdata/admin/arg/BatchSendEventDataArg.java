package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class BatchSendEventDataArg {
    private List<EventData> eventDatas;

    @Data
    public static class EventData {
        private Integer sourceEventType;
        private Integer sourceTenantType;
        private ObjectData sourceData;

    }
}
