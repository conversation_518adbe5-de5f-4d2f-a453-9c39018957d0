package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:41 2022/10/9
 * @Desc:
 */
@Data
@ApiModel
public class GetByIdApiStatusArg implements Serializable {
    @ApiModelProperty("拆分对象apiName")
    private String splitObjectApiName;
    @ApiModelProperty("状态，开启：true,关闭：false")
    private Boolean status=true;
}
