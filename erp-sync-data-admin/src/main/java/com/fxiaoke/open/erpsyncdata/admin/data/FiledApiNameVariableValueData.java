package com.fxiaoke.open.erpsyncdata.admin.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FiledApiNameVariableValueData implements Serializable {
    @ApiModelProperty("映射对应夫的key\n" + "\"account_owner\", \"负责人(数据源企业关联客户的负责人)\"\n" + "\"partner_owner\", \"负责人(数据源企业关联合作伙伴的负责人)\"\n" + "\"account_source\", \" 来源(数据源企业关联的客户)\"\n\"partner_source\", \"来源(数据源企业关联的合作伙伴)\"\n\"priority_relation_owner\", \"负责人（默认匹配）\"\n" + "\"object_data_reflect_outer_owner\", \"外部负责人(同步数据的负责人映射的对接人)\"\n\"outer_relation_owner\", \"外部负责人(数据源方主负责人)\"\n" + "\"downstream_relation_owner\", \"负责人(目标方的主负责人)\"")
    private String value;
    @ApiModelProperty("前端展示的描述")
    private String description;

    private FiledApiNameVariableValueData() {
    }

    public FiledApiNameVariableValueData(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
