package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface GetAllErpObjApiType {
    @Data
    class Arg implements Serializable {
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        private List<ApiType> apiTypes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ApiType implements Serializable {
        private String type;
        private String name;
    }
}
