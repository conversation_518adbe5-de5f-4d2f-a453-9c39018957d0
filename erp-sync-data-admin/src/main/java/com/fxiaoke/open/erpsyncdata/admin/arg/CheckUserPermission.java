package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel
public interface CheckUserPermission {
    @Data
    class Arg implements Serializable {
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        /**
         * 自定义函数中是否展示集成对象API的tab
         * true-有权限
         */
        private Boolean hasErpdssPermission;
    }
}
