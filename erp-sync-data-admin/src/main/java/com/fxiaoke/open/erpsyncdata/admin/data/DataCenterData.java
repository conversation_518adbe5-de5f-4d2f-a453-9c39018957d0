package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @Date: 16:22 2022/2/16
 * @Desc:
 */
@Data
@ApiModel
public class DataCenterData implements Serializable {
    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("数据中心id")
    private String dcId;
    @ApiModelProperty("数据中心名称")
    private String dcName;
    @ApiModelProperty("数据中心渠道")
    private ErpChannelEnum dcChannel;
    private String connectorKey;
    private String iconUrl;

    public static DataCenterData newDataByDcId(String tenantId, String dcId, ErpConnectInfoDao erpConnectInfoDao, I18NStringManager i18NStringManager, String lang) {
        ErpConnectInfoEntity entity = null;
        if (erpConnectInfoDao != null && tenantId != null && dcId != null) {
            entity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dcId);
        }
        if (entity != null) {
            return newDataByEntity(entity);
        } else {
            return newDataWithoutName(tenantId, dcId);
        }
    }

    private static DataCenterData newDataWithoutName(String tenantId, String dcId) {
        DataCenterData data = new DataCenterData();
        data.setTenantId(tenantId);
        data.setDcId(dcId);
        return data;
    }


    @NotNull
    public static DataCenterData newDataByEntity(@NotNull ErpConnectInfoEntity entity) {
        DataCenterData data = new DataCenterData();
        data.setTenantId(entity.getTenantId());
        data.setDcId(entity.getId());
        if (entity.getChannel() == ErpChannelEnum.CRM) {
            data.setDcName(entity.getChannel().getNameByTraceLocale());
        } else {
            data.setDcName(entity.getDataCenterName());
        }
        Connector connector = AllConnectorUtil.getByChannelAndConnectParam(entity.getChannel(), entity.getConnectParams());
        data.setDcChannel(entity.getChannel());
        data.setConnectorKey(connector.getKey());
        data.setIconUrl(connector.getIconUrl());
        return data;
    }

}
