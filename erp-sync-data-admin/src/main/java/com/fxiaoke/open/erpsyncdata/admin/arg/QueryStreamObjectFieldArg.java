package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class QueryStreamObjectFieldArg {
    @ApiModelProperty("拆分后的erp对象名字")
    private String splitErpObjectApiName;
    @ApiModelProperty("字段名字")
    private String erpFieldApiName;
}
