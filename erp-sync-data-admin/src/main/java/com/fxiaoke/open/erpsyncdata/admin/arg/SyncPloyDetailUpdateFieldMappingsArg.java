package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailUpdateFieldMappingsArg implements Serializable {
    @ApiModelProperty("策略明细id")
    private String id;
    @ApiModelProperty("主对象映射")
    private ObjectMappingArg masterObjectMapping;
    @ApiModelProperty("从对象映射")
    private List<ObjectMappingArg> detailObjectMappings;

    @Data
    @ApiModel
    public static class ObjectMappingArg implements Serializable {
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("字段映射列表，每条数据对应一个字段的映射关系")
        private List<FieldMappingArg> fieldMappings;
    }

    @Data
    @ApiModel
    public static class FieldMappingArg implements Serializable {
        @ApiModelProperty("源企业对象字段的apiName")
        private String sourceApiName;
        @ApiModelProperty("源企业对象字段的类型")
        private String sourceType;
        @ApiModelProperty("源对象关联字段所对应对象的apiName")
        private String sourceTargetApiName;
        @ApiModelProperty("源对象引用类型，所应用的字段对应的类型")
        private String sourceQuoteFieldType;

        private String sourceQuoteRealField;
        private String sourceQuoteFieldTargetObjectApiName;
        private String sourceQuoteFieldTargetObjectField;
        @ApiModelProperty("目标对象字段apiName")
        private String destApiName;
        @ApiModelProperty("目标对象字段类型")
        private String destType;
        @ApiModelProperty("关联字段所对应对象的apiName")
        private String destTargetApiName;
        @ApiModelProperty("目标对象引用类型，所应用的字段对应的类型\"")
        private String destQuoteFieldType;
//        @ApiModelProperty("选项")
//        private Map<Object, Object> optionMapping;
        @ApiModelProperty("选项")
        private List<OptionMappingArg> optionMappings;
        @ApiModelProperty("映射类型 ： 1、原对象取值，2、原对象引用取值，3、固定值，4、循环分配")
        private Integer mappingType;
        @ApiModelProperty("自定义函数")
        private String function;
        @ApiModelProperty("固定值和循环分配时的值, json字符串")
        private String value;
        @ApiModelProperty("默认值")
        private String defaultValue;
        @ApiModelProperty("值类型，固定值1，默认值2")
        private Integer valueType;//值类型，固定值1，默认值2
        @ApiModelProperty("是否不更新该字段，true：不更新，其他：更新")
        private Boolean notUpdateField;
        @ApiModelProperty("是否不校验该字段映射，true：不校验，其他：校验")
        private Boolean notCheckMappingField;

        /**
         * 直接使用原值 ,, 同时对默认值、固定值 会不执行trim
         */
        private Boolean useSourceValueDirectly;
        /**
         * 什么值判断为null
         * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.NullCandidateEnum
         */
        private List<String> nullCandidateList;
    }

    @Data
    @ApiModel
    public static class OptionMappingArg implements Serializable {
        @ApiModelProperty("源选项")
        private Object sourceOption;
        @ApiModelProperty("目标选项")
        private Object destOption;
    }

}
