package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateEventConfigModel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:45 2022/10/10
 * @Desc:
 */
@ApiModel
@Data
public class ApiMessageData implements Serializable {
    @ApiModelProperty("接口简单url")
    public ErpObjInterfaceUrlEnum interfaceSimpleUrl;
    @ApiModelProperty("接口名称")
    public String interfaceName;
    @ApiModelProperty("接口详细url")
    public String interfaceDetailUrl;
    /**
     * 部分接口一次会调用多个url,比如K3新增(新增,提交,审核)等
     */
    @ApiModelProperty("接口详细url")
    public List<String> interfaceDetailUrls;
    @ApiModelProperty("自定义函数apiName")
    public String funcApiName;

    @ApiModelProperty("云星空旗舰版事件订阅token")
    public String token;
    @ApiModelProperty("云星空旗舰版事件订阅配置")
    public List<K3UltimateEventConfigModel> eventConfigList;

    @ApiModelProperty("webhookUrl")
    private String webhookUrl;
    @ApiModelProperty("连接器的函数ApiName")
    public String datacenterFuncApiName;
}
