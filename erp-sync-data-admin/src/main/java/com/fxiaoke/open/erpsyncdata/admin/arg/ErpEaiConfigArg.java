package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class ErpEaiConfigArg implements Serializable {

    @ApiModelProperty(value = "对象名称")
    private String objName;
    @ApiModelProperty(value = "虚拟对象apiName")
    private String splitObjectApiName;
    @ApiModelProperty(value = "拆分类型")
    private String splitType;
    @ApiModelProperty(value = "对象扩展信息")
    private String erpObjectExtendValue;

    @ApiModelProperty(value = "数据id")
    private String id;
    @ApiModelProperty(value = "企业id")
    private String tenantId;
    @NotNull
    @ApiModelProperty(value = "对象apiName[非空]")
    private String objApiName;
    @ApiModelProperty(value = "数据中心ID")
    private String dataCenterId;
    @NotNull
    @ApiModelProperty(value = "api接口主键字段[非空]")
    private String idField;
    @NotNull
    @ApiModelProperty(value = "数据主键字段[非空]")
    private String dbKey;
    @ApiModelProperty(value = "查询对象数据sql语句")

    private String querySql;//查询数据sql
    @ApiModelProperty(value = "对象查询时间条件")
    private String dateTimeConditionField;
    @ApiModelProperty(value = "EAI报文标签")
    private String label;
    @ApiModelProperty(value = "查询ID的sql语句")
    private String queryIdSql;
    @ApiModelProperty(value = "新增sql语句")
    private List<String> insertSqls= Arrays.asList("");
    @ApiModelProperty(value = "更新sql语句")
    private List<String> udpateSqls= Arrays.asList("");

    @ApiModelProperty(value = "明细")
    private Map<String, ErpEaiDetailConfigArg> details = new HashMap<>();
    @ApiModelProperty(value = "最后修改人[系统指定]")
    private String lastModifyBy;


    @Data
    @ApiModel
    public static class ErpEaiDetailConfigArg{
        @ApiModelProperty(value = "对象名称")
        private String objName;
        @ApiModelProperty(value = "虚拟对象apiName")
        private String splitObjectApiName;
        @ApiModelProperty(value = "拆分类型")
        private String splitType;
        @ApiModelProperty(value = "对象扩展信息")
        private String erpObjectExtendValue;

        @ApiModelProperty(value = "数据id")
        private String id;
        @ApiModelProperty(value = "企业id")
        private String tenantId;
        @NotNull
        @ApiModelProperty(value = "对象apiName[非空]")
        private String objApiName;
        @ApiModelProperty(value = "数据中心ID")
        private String dataCenterId;
        @NotNull
        @ApiModelProperty(value = "api接口主键字段[非空]")
        private String idField;
        @NotNull
        @ApiModelProperty(value = "数据主键字段[非空]")
        private String dbKey;
        @ApiModelProperty(value = "查询对象数据sql语句")
        private String querySql;//查询数据sql
        @ApiModelProperty(value = "对象查询时间条件")
        private String dateTimeConditionField;
        @ApiModelProperty(value = "EAI报文标签")
        private String label;
        @ApiModelProperty(value = "查询ID的sql语句")
        private String queryIdSql;
        @ApiModelProperty(value = "新增sql语句")
        private List<String> insertSqls= Arrays.asList("");
        @ApiModelProperty(value = "更新sql语句")
        private List<String> udpateSqls= Arrays.asList("");
        @ApiModelProperty(value = "最后修改人[系统指定]")
        private String lastModifyBy;
    }

}
