package com.fxiaoke.open.erpsyncdata.admin.data;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class FieldMappingData implements Serializable {
    private String sourceApiName;
    private String sourceType;
    private String sourceTargetApiName;
    private String sourceQuoteFieldType;
    private String sourceQuoteRealField;
    private String sourceQuoteFieldTargetObjectApiName;
    private String sourceQuoteFieldTargetObjectField;
    private String destApiName;
    private String destType;
    private String destTargetApiName;
    private String destQuoteFieldType;
    //    private Map<Object, Object> optionMapping;
    private List<OptionMappingData> optionMappings;
    private Integer mappingType;
    private String function;
    private String value;

    public Object getDestOptionBySourceOption(Object sourceOption) {
        if (this.optionMappings == null) {
            return null;
        }
        for (OptionMappingData optionMapping : this.getOptionMappings()) {
            if (optionMapping.getSourceOption().equals(sourceOption)) {
                return optionMapping.getDestOption();
            }
        }
        return null;
    }
}
