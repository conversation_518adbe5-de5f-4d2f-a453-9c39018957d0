package com.fxiaoke.open.erpsyncdata.admin.data;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/3.
 */
@Data
public class ObjectMappingData implements Serializable {
    private String sourceObjectApiName;
    private String destObjectApiName;
    private List<FieldMappingData> fieldMappings = new ArrayList<>();

    private ObjectMappingData() {
    }

    public static ObjectMappingData newData(String sourceObjectApiName, String destObjectApiName, List<FieldMappingData> fieldMappings) {
        ObjectMappingData objectMappingData = new ObjectMappingData();
        objectMappingData.setSourceObjectApiName(sourceObjectApiName);
        objectMappingData.setDestObjectApiName(destObjectApiName);
        objectMappingData.setFieldMappings(fieldMappings);
        return objectMappingData;
    }
}