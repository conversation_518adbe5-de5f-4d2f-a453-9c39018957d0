package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/13.
 */
@Data
public class ListSyncButtonPloyDetailsData implements Serializable {
    @ApiModelProperty("快照id")
    private String id;
    @ApiModelProperty("源企业id")
    private String sourceTenantId;
    @ApiModelProperty("源企业名称")
    private String sourceTenantName;
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;;
    @ApiModelProperty("源企业对象DisplayName")
    private String sourceObjectDisplayName;;
    @ApiModelProperty("目标企业Id")
    private String destTenantId;
    @ApiModelProperty("目标企业名称")
    private String destTenantName;
    @ApiModelProperty("目标企业类型")
    private Integer destTenantType;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("源企业对象DisplayName")
    private String destObjectDisplayName;;
    /** 策略状态 1.启用 2.停用 {@link  SyncPloyDetailStatusEnum} */
    private Integer status;
    /** 策略对象主键id */
    private String syncPloyId;
    /** 策略对象所含详情主键id */
    private String syncPloyDetailId;
    @ApiModelProperty("策略名称")
    private String ployName;
    @ApiModelProperty("策略明细启用情况")
    private String statusName;

}

