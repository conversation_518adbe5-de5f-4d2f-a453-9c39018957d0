package com.fxiaoke.open.erpsyncdata.admin.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EmployeeSimpleData implements Serializable {
    public static final String UnboundAppName = "未绑定";   // ignoreI18n  已作废
    private static final long serialVersionUID = 1L;
    private String enterpriseAccount;
    private Long outerTenantId;
    private Integer employeeId;
    private String enterpriseName;
    @ApiModelProperty("对接人outerUid")
    private Long outerUid;
    private String employeeName;
    private String nameSpell;//员工姓名拼音表示
    private String mobile;
    private String email;
    private String post;
    private String department;
    private String jobIntroduce;
    /**
     * 对接状态
     */
    private Integer type;
    private String profileImage;
    private String profileImageFullPath; //头像完整url
    private List<EnterpriseSimpleData> enterpriseSimpleVos;
    private Long employeeCardId;
    /**
     * 标识上游？下游对接人,0上游，1下游，当前仅在查看对方对接人时用到
     */
    private Integer sourceType;
    /**
     * 全局对接人标记,1全局对接人，0普通对接人
     */
    private Integer publicType;
    //角色列表，仅当上游查看下游对接人时返回
    private List<RoleData> roles;
    /**
     * 是否对接负责人
     */
    private Boolean relationOwner = false;
    /**
     * 是否数据管理员
     */
    private Boolean dataManager = false;
    /**
     * 激活状态，{@link com.facishare.er.api.model.enums.ActiveStateEnum},仅在查看对方下游对接人时填充
     */
    private Integer activeState;
    /**
     * 注册连接方式 1:管理员添加 2:自注册 {@link com.facishare.er.api.model.enums.RegisterType}
     */
    private Integer registerType;
    /**
     * 关联联系人id
     */
    private String mapperContactId;
    /**
     * 关联联系人的状态
     */
    private Integer mapperStatus;
    /**
     * 已绑定的我方微信服务号
     */
    private String bindingsWeChatAppName = UnboundAppName;
    //绑定时间
    private String boundWechatTime = "-";
    //是否关注公众号
    private String isStar = "-";
    //关注时间
    private String starTime = "-";
}
