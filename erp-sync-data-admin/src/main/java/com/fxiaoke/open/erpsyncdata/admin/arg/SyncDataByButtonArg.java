package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncDataByButtonArg implements Serializable {
    @ApiModelProperty("待同步对象apiName")
    private String objectApiName;
    @ApiModelProperty("待同步数据的id列表")
    private List<String> dataIds;
    @ApiModelProperty("同步数据使用的策略明细Id")
    private String syncPloyDetailSnapshotId;
}
