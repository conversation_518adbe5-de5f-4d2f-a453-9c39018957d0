package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncDataMappingResyncArg implements Serializable {
    @ApiModelProperty("数据映射id, （源数据id + 目标数据id ～= 数据映射id）" )
    private List<String> ids;
    @ApiModelProperty("源对象ApiName" )
    private String sourceObjectApiName;
    @ApiModelProperty("源数据id" )
    private String sourceDataId;
    @ApiModelProperty("策略明细id")
    private String ployDetailId;
}