package com.fxiaoke.open.oasyncdata.controller.oa.open;


import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.OABrushDataManger;
import com.fxiaoke.open.oasyncdata.manager.UserLoginManager;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAMessageResyncRule;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OAUserAuthService;
import com.fxiaoke.open.oasyncdata.util.AESEncryptUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import eu.bitwalker.useragentutils.DeviceType;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/18
 */
@Slf4j
@Api(tags = "OA鉴权回调相关接口")
@RestController("authOAController")
@RequestMapping("erp/syncdata/open/oa")
public class OAUserAuthController {
    @Autowired
    private OAUserAuthService oaUserAuthService;

    @Autowired
    private UserLoginManager userLoginManager;

    @Autowired
    private UserManager userManager;
    @Autowired
    private OAObjectFieldService oaObjectFieldService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OABrushDataManger oaBrushDataManger;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;



    @ApiOperation(value = "新增或更新字段")
    @ResponseBody
    @RequestMapping(value = "/hello", method = RequestMethod.POST)
    public Result<String> addOrUpdateObjectField() {
        log.info("hello ....");
        return Result.newSuccess();
    }

    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/hello2", method = RequestMethod.POST)
    public Result<String> addOrUpdateObjectField2() {
        log.info("hello ....");
        return Result.newSuccess();
    }


    @ApiOperation(value = "复星web鉴权")
    @RequestMapping(value = "/authorizeWeb/{apiName}/{dataId}/{ei}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeWeb(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                               String ticket, HttpServletRequest request) {
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            result = oaUserAuthService.authorize(ei, ticket, apiName, dataId, false);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, false,request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(redirectUrl,i18NStringManager.getByEi(I18NStringEnum.s1265,ei)+urlResult.getErrMsg(), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;

    }

    @ApiOperation(value = "复星app鉴权")
    @ResponseBody
    @RequestMapping(value = "/authorizeApp/{apiName}/{dataId}/{ei}", method = RequestMethod.GET)
    public Object authorizeApp(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                               String ticket, HttpServletRequest request) {
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            result = oaUserAuthService.authorize(ei, ticket, apiName, dataId, true);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeApp:{}", result);
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, true, request);
            log.info("urlResultApp:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "致远web鉴权")
    @RequestMapping(value = "/authorizeWeb/SeeYon/{apiName}/{dataId}/{ei}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeSeeYonTask(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                                      @PathVariable("isApp") Boolean isApp, String v5ticket, HttpServletRequest request) {
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);

        try {
            result = oaUserAuthService.authorizeSeeYon(ei, v5ticket);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "致远门户免登录跳转")
    @RequestMapping(value = "/authorizeWeb/SeeYon/login/{ei}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeSeeYonSSO(@PathVariable("ei") String ei,
                                     @PathVariable("isApp") Boolean isApp, String ticket,
                                     HttpServletRequest request) {
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            result = oaUserAuthService.authorizeSeeYon(ei, ticket);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginMainPage(Integer.valueOf(ei), result.getData(), isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }


//    // 目前双胞胎不做鉴权，齐鲁手机端
//    @ApiOperation(value = "通用不带验证跳转")
//    @RequestMapping(value = "/authorize/common/{apiName}/{dataId}/{ei}/{userId}/{isApp}", method = RequestMethod.GET)
//    public String authorizeCommon(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
//                                  @PathVariable("userId") String userId, @PathVariable("isApp") Object isApp) {
//        Result<String> result = null;
//        if (!ConfigCenter.SSO_ENTERPRISE_EI.contains(ei)) {
//            return "not available";
//        }
//        String redirectUrl = "账号认证失败";
//        String userCode = userManager.getFxUserCode(userId, ei);
//        if (StringUtils.isEmpty(userCode)) {
//            return redirectUrl;
//        }
//        Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), userCode, apiName, dataId, Boolean.valueOf(isApp.toString()));
//        log.info("urlResult common:{}", urlResult);
//        if (urlResult.isSuccess()) {
//            redirectUrl = "redirect:" + urlResult.getData();
//        }
//        return redirectUrl;
//    }
    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/field/addOrUpdateObjectField", method = RequestMethod.POST)
    public Result<List> addOrUpdateObjectField(@RequestBody List<OAObjectFieldVO> oaObjectFields,
                                               @RequestHeader(value = "secret")String secret,
                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }
        String tenantId = DataBaseBatchIndexUtil.notTenantId;
        return oaObjectFieldService.addOrUpdateObjectField(tenantId, oaObjectFields,lang);
    }

    @ApiOperation(value = "刷数据")
    @RequestMapping(value = "/field/brushData", method = RequestMethod.POST)
    public Result<List> brushData(@RequestBody List<OAObjectFieldVO> oaObjectFields,
                                               @RequestHeader(value = "secret")String secret,
                                               @RequestHeader(value = "tenantId",required = false)String tenantIdParams,
                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }
        String tenantId =null;
        if(StringUtils.isNotBlank(tenantIdParams)){
            tenantId=tenantIdParams;
        }

        oaBrushDataManger.fillDataCenterIdApi(tenantId);
        return  Result.newSuccess();
    }


    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/field/addOrUpdateObjectFieldByApiName", method = RequestMethod.GET)
    public Result<List> addOrUpdateObjectFieldByApiName(@RequestBody List<OAObjectFieldVO> oaObjectFields,
                                                        @Param(value = "secret") String secret,
                                                        @Param(value = "tenantId") String tenantId,
                                                        @Param(value = "objApiName") String objApiName,
                                                        @Param(value = "prefix") String prefix,
                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }
        String prefixValue="#"+prefix;
        return oaObjectFieldService.addOrUpdateObjectFieldByApiName(tenantId, objApiName,prefixValue,lang);
    }

    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/field/addOrUpdateSupportField", method = RequestMethod.GET)
    public Result<List> addOrUpdateSupportField(@RequestBody OAObjectFieldVO objectFieldVO,
                                                        @Param(value = "secret") String secret,@Param(value = "objApiName") String objApiName,
                                                        @Param(value = "replaceName") String replaceName,
                                                        @Param(value = "fileApiName") String fileApiName,
                                                        @Param(value = "label") String label,
                                                @Param(value = "tenantId") String tenantId
    ) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }

        String prefixValue="#"+replaceName;
        return oaObjectFieldService.addOrUpdateSupportField(tenantId, objApiName,prefixValue,fileApiName,label);
    }

    @ApiOperation(value = "刷库数据")
    @RequestMapping(value = "/field/brushSettings", method = RequestMethod.GET)
    public Result<List> brushSettings( @Param(value = "secret") String secret) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }
        List<String> allTenantIds = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
        List<String> tenantIds = allTenantIds.stream().distinct().collect(Collectors.toList());//去重
        for (String tenantIdSetting : tenantIds) {
            Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId(tenantIdSetting);
            if(ConfigCenter.NOT_RE_SYNC_TENANT.contains(tenantIdSetting)){
                if(CollectionUtils.isNotEmpty(listResult.getData())){
                    for (OAConnectInfoVO datum : listResult.getData()) {
                        String dataCenterId=datum.getId();
                        OASettingVO oaSettingVO=new OASettingVO();
                        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                        oaSettingVO.setTenantId(tenantIdSetting);
                        oaSettingVO.setDataCenterId(dataCenterId);
                        OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                        oaMessageResyncRule.setStatus(Boolean.FALSE);
                        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                        oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                    }
                }
                continue;
            }
            if(ConfigCenter.OA_ONLY_RETRY_DEAL_FAILED_EI_LIST.contains(tenantIdSetting)){
                if(CollectionUtils.isNotEmpty(listResult.getData())){
                    for (OAConnectInfoVO datum : listResult.getData()) {
                        String dataCenterId=datum.getId();
                        OASettingVO oaSettingVO=new OASettingVO();
                        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                        oaSettingVO.setTenantId(tenantIdSetting);
                        oaSettingVO.setDataCenterId(dataCenterId);
                        OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                        oaMessageResyncRule.setStatus(Boolean.TRUE);
                        oaMessageResyncRule.setMessageStatus(Lists.newArrayList("0","2"));
                        oaMessageResyncRule.setResyncCounts(2);
                        oaMessageResyncRule.setIntervalMinutes(10);
                        oaMessageResyncRule.setBusinessTypes(Lists.newArrayList("crmToDo"));
                        oaMessageResyncRule.setResyncEventTypes(Lists.newArrayList("2","3"));
                        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                        oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                    }
                }
                continue;
            }
            if(CollectionUtils.isNotEmpty(listResult.getData())){
                for (OAConnectInfoVO datum : listResult.getData()) {
                    String dataCenterId=datum.getId();
                    OASettingVO oaSettingVO=new OASettingVO();
                    oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                    oaSettingVO.setTenantId(tenantIdSetting);
                    oaSettingVO.setDataCenterId(dataCenterId);
                    OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                    oaMessageResyncRule.setStatus(Boolean.TRUE);
                    oaMessageResyncRule.setResyncCounts(2);
                    oaMessageResyncRule.setMessageStatus(Lists.newArrayList("0","2"));
                    oaMessageResyncRule.setIntervalMinutes(10);
                    oaMessageResyncRule.setBusinessTypes(Lists.newArrayList("crmToDo"));
                    oaMessageResyncRule.setResyncEventTypes(Lists.newArrayList("1","2","3"));
                    oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                    oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                }
            }

        }
        return Result.newSuccess();
    }

    @ApiOperation(value = "log")
    @RequestMapping(value = "/field/log", method = RequestMethod.GET)
    public Result<List> log(){
        log.info("log out password:{}","password:9098998877888912124884");
        String scriptCode="exec groovy code:import groovy.json.JsonSlurper\n" +
                "  import sun.net.www.protocol.https.DelegateHttpsURLConnection\n" +
                "  import javax.crypto.Mac\n" +
                "  import javax.crypto.spec.SecretKeySpec\n" +
                "  \n" +
                "  public String getToken() {\n" +
                "      String token = null;\n" +
                "      String userName = \"fxxk\";       // 填写客户提供的账号\n" +
                "      String password = \"2a4944da-ecec-4a9c-8f00-140a43d43ba89009090901\";   // 填写客户提供的密码\n" +
                "      String requestUrl = \"http://**************:9095/seeyon/rest/token/\" + userName + \"/\" + password;  // 修改请求地址\n" +
                "      HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\n" +
                "      connection.setRequestMethod(\"GET\");\n" +
                "              // 设置连接主机服务器的超时时间：15000毫秒\n" +
                "              connection.setConnectTimeout(15000); connection.setUseCaches(false); connection.setRequestProperty(\"Content-Type\", \"text/xml; charset=UTF-8\");\n" +
                "                  // 设置读取远程返回的数据时间：60000毫秒\n" +
                "                  connection.setReadTimeout(60000); connection.setDoOutput(true); connection.setDoInput(true); connection.connect(); int code = connection.getResponseCode();\n" +
                "                  if (code == 200) {\n" +
                "                      InputStream inputStream = connection.getInputStream();\n" +
                "                      BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\n" +
                "                      String line;\n" +
                "                      StringBuffer buffer = new StringBuffer();\n" +
                "                      while ((line = bufferedReader.readLine()) != null) {\n" +
                "                          buffer.append(line);\n" +
                "                      }\n" +
                "                      String str = buffer.toString();\n" +
                "                      def jsonSlurper = new JsonSlurper()\n" +
                "                      Map map = jsonSlurper.parseText(str)\n" +
                "                      token = map.get(\"id\")\n" +
                "                      }\n" +
                "                      else {\n" +
                "                          throw new RuntimeException(\"Handshake abnormality(\" + connection.getResponseCode() + \")！\" + connection.getResponseMessage())\n" +
                "                          }\n" +
                "                          return url + \"?token=\" + token;\n" +
                "                      }\n" +
                "                      url = getToken();\n" +
                "                      return url;, get exception: javax.script.ScriptException: javax.script.ScriptException: java.lang.RuntimeException: Handshake abnormality(401)";
        log.error("exec groovy code:{}, get exception: ", scriptCode);
        log.info("log out password:{}","password:9098998877888912124884");
        return Result.newSuccess();
    }

    @ApiOperation(value = "织语门户免登录跳转")
    @RequestMapping(value = "/authorizeWeb/ccwork/login/{ei}/{isApp}/{token}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeCCWorkSSO(@PathVariable("ei") String ei, @PathVariable("isApp") Boolean isApp, @PathVariable("token") String token,
                                     String appid, String msgSignature, String timeStamp, String nonce, String encrypt,
                                     HttpServletRequest request) {
        log.info("织语门户跳转url参数，ei:{},isApp:{},token:{}", ei, isApp, token);
        log.info("织语门户跳转拼接参数，appid:{},msgSignature:{},timestamp:{},nonce:{},encrypt:{}", appid, msgSignature, timeStamp, nonce, encrypt);
        log.info("拼接参数为：appid:{},msgSignature:{},timeStamp:{},nonce:{},encrypt:{}", appid, msgSignature, timeStamp, nonce, encrypt);
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String appKey = "cHmz9h3u3t2fPfKL9giIuwrZ9TIiuJOJ";
            Result<String> jsonResult = AESEncryptUtil.decrypt(appKey, token, msgSignature, timeStamp, nonce, encrypt);
            if (!jsonResult.isSuccess() || StringUtils.isEmpty(jsonResult.getData())) {
                log.warn("authorizeCCWork decrypt fail, message={}", jsonResult.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(jsonResult.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            Map<String, String> resultMap = GsonUtil.fromJson(jsonResult.getData(), Map.class);
            String ticket = resultMap.get("ticket");
            Result<String> result = null;
            result = oaUserAuthService.authorizeCCWork(ei, ticket, nonce);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorizeCCWork get user fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginListPage(Integer.valueOf(ei), result.getData(), isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "织语跳转待办详情")
    @RequestMapping(value = "/authorizeWeb/ccwork/{apiName}/{dataId}/{ei}/{isApp}/{token}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeCCWorkDetailTask(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId,
                                            @PathVariable("ei") String ei,
                                            @PathVariable("isApp") Boolean isApp, @PathVariable("token") String token,
                                            String appid, String msgSignature, String timeStamp, String nonce, String encrypt,
                                            HttpServletRequest request) {
        encrypt = encrypt.replace(" ", "+");
        log.info("拼接参数为：appid:{},msgSignature:{},timeStamp:{},nonce:{},encrypt:{}", appid, msgSignature, timeStamp, nonce, encrypt);
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String appKey = "cHmz9h3u3t2fPfKL9giIuwrZ9TIiuJOJ";
            Result<String> jsonResult = AESEncryptUtil.decrypt(appKey, token, msgSignature, timeStamp, nonce, encrypt);
            if (!jsonResult.isSuccess() || StringUtils.isEmpty(jsonResult.getData())) {
                log.warn("authorizeCCWork decrypt fail, message={}", jsonResult.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(jsonResult.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            Map<String, String> resultMap = GsonUtil.fromJson(jsonResult.getData(), Map.class);
            String ticket = resultMap.get("ticket");
            Result<String> result = null;
            result = oaUserAuthService.authorizeCCWork(ei, ticket, nonce);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorizeCCWork get user fail, message={}", result.getErrMsg());
                return redirectUrl;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginForQihoo(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "明德跳转待办详情(泛微OA)")
    @RequestMapping(value = "/authorizeWeb/MD/{apiName}/{dataId}/{ei}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeMD(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                              @PathVariable("isApp") Boolean isApp, String loginid, String stamp, String token, HttpServletRequest request) {
        log.info("明德跳转的拼接参数为：loginid:{},stamp:{},token:{}", loginid, stamp, token);
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        Result<String> urlResult = null;
        try {
            result = oaUserAuthService.authorizeMD(token, loginid, stamp, ei);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        log.info("明德跳转重定向地址为：{}", urlResult.getData());
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }


    @ApiOperation(value = "蓝凌跳转待办详情")
    @RequestMapping(value = "/authorizeWeb/LL/{apiName}/{dataId}/{ei}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeLLDetail(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                                    @PathVariable("isApp") Boolean isApp, String ticket, HttpServletRequest request) {
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String url = oaAuthorWebUrl + "LL/" + apiName + "/" + dataId + "/" + ei + "/" + isApp;
            result = oaUserAuthService.authorizeLL(ei, url, ticket, isApp);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "蓝凌对象页面跳转")
    @RequestMapping(value = "/authorizeWeb/LL/Object/{ei}/{apiName}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeLLObjectPage(@PathVariable("ei") String ei, @PathVariable("apiName") String apiName,
                                        String ticket, HttpServletRequest request) {
        log.info("蓝凌对象页面跳转，ei:{},apiName:{},ticket:{}", ei, apiName, ticket);
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String url = oaAuthorWebUrl + "LL/Object/" + ei + "/" + apiName;
            result = oaUserAuthService.authorizeLL(ei, url, ticket, false);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorizeLLWork get user fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginObjectPage(Integer.valueOf(ei), result.getData(), apiName, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "蓝凌对象页面跳转")
    @RequestMapping(value = "/LL/authorizeWeb/url/{ei}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeLLPage(@PathVariable("ei") String ei, String url,
                                  String ticket, HttpServletRequest request) {
        log.info("蓝凌对象页面跳转，ei:{},url:{},ticket:{}", ei, url, ticket);
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String path = "https://www.fxiaoke.com/erp/syncdata/open/oa/LL/authorizeWeb/url/" + ei + "?url=" + URLEncoder.encode(url);
            result = oaUserAuthService.authorizeLL(ei, path, ticket, false);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorizeLLWork get user fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginPage(Integer.valueOf(ei), result.getData(), url, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "蓝凌门户免登录跳转")
    @RequestMapping(value = "/authorizeWeb/LL/login/{ei}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeLLMainPage(@PathVariable("ei") String ei, @PathVariable("isApp") Boolean isApp,
                                      String ticket, HttpServletRequest request) {
        log.info("蓝凌门户跳转url参数，ei:{},isApp:{},ticket:{}", ei, isApp, ticket);
        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            String url = oaAuthorWebUrl + "LL/login/" + ei + "/" + isApp;
            result = oaUserAuthService.authorizeLL(ei, url, ticket, isApp);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("authorizeLLWork get user fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            log.info("authorizeWeb:{}", result);
            Result<String> urlResult = userLoginManager.avoidLoginMainPage(Integer.valueOf(ei), result.getData(), isApp, request);
            log.info("urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "万事利跳转待办详情")
    @RequestMapping(value = "/authorizeWeb/Wensli/{apiName}/{dataId}/{ei}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizeWensli(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei, @RequestHeader("user-agent") String userAgent,
                                  String ticket, HttpServletRequest request) {

        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            if (StringUtils.isEmpty(ticket)) {
                log.warn("ticket为空，ei={}", ei);
                return modelAndView;
            }
            result = oaUserAuthService.authorizeWensli(ei, ticket, apiName, dataId);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("Wensli authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }

            log.info("Wensli authorizeWeb:{}", result);
            boolean isApp = false;
            try {
                //useragent不为空且为移动端，isApp为true
                log.info("Wensli userAgent:{}", userAgent);
                if (!StringUtils.isEmpty(userAgent)) {
                    UserAgent userAgentObj = UserAgent.parseUserAgentString(userAgent);
                    OperatingSystem operatingSystem = userAgentObj.getOperatingSystem();
                    DeviceType deviceType = operatingSystem.getDeviceType();
                    log.info("Wensli device:{}", deviceType);
                    if (DeviceType.MOBILE == deviceType || DeviceType.TABLET == deviceType) {
                        isApp = true;
                    }
                }
            } catch (Exception e) {
                log.info("Wensli userAgent error,", e);
            }
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("Wensli urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "统一支持云之家代办跳转")
    @RequestMapping(value = "/authorizeWeb/webhook/{apiName}/{dataId}/{ei}", method = RequestMethod.GET)
    public Object authorizeWebhook(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei, @RequestHeader("user-agent") String userAgent,
                                   String ticket, HttpServletRequest request) {

        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            if (StringUtils.isEmpty(ticket)) {
                log.warn("ticket为空，ei={}", ei);
                return modelAndView;
            }
            result = oaUserAuthService.authorizeWebHook(ei, ticket, apiName, dataId);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("webhook authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }

            log.info("webhook authorizeWeb:{}", result);
            boolean isApp = false;
            try {
                //useragent不为空且为移动端，isApp为true
                log.info("webhook userAgent:{}", userAgent);
                if (!StringUtils.isEmpty(userAgent)) {
                    UserAgent userAgentObj = UserAgent.parseUserAgentString(userAgent);
                    OperatingSystem operatingSystem = userAgentObj.getOperatingSystem();
                    DeviceType deviceType = operatingSystem.getDeviceType();
                    log.info("webhook device:{}", deviceType);
                    if (DeviceType.MOBILE == deviceType || DeviceType.TABLET == deviceType) {
                        isApp = true;
                    }
                }
            } catch (Exception e) {
                log.info("webhook userAgent error,", e);
            }
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), result.getData(), apiName, dataId, isApp, request);
            log.info("webhook urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }


    @ApiOperation(value = "支持云之家免登")
    @RequestMapping(value = "/authorizePortalWebhook/webhook/{ei}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizePortalWebhook( @PathVariable("ei") String ei, @RequestHeader("user-agent") String userAgent,
                                          String ticket, HttpServletRequest request) {

        Result<String> result = null;
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            if (StringUtils.isEmpty(ticket)) {
                log.warn("ticket为空，ei={}", ei);
                return modelAndView;
            }
            result = oaUserAuthService.authorizeWebHook(ei, ticket, StringUtils.EMPTY, StringUtils.EMPTY);
            if (!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
                log.warn("webhook authorize fail, message={}", result.getErrMsg());
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }

            log.info("webhook authorizeWeb:{}", result);
            boolean isApp = false;
            try {
                //useragent不为空且为移动端，isApp为true
                log.info("webhook userAgent:{}", userAgent);
                if (!StringUtils.isEmpty(userAgent)) {
                    UserAgent userAgentObj = UserAgent.parseUserAgentString(userAgent);
                    OperatingSystem operatingSystem = userAgentObj.getOperatingSystem();
                    DeviceType deviceType = operatingSystem.getDeviceType();
                    log.info("webhook device:{}", deviceType);
                    if (DeviceType.MOBILE == deviceType || DeviceType.TABLET == deviceType) {
                        isApp = true;
                    }
                }
            } catch (Exception e) {
                log.info("webhook userAgent error,", e);
            }
            Result<String> urlResult = userLoginManager.avoidLoginMainPage(Integer.valueOf(ei), result.getData(), isApp, request);
            log.info("webhook urlResultWeb:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }




    @ApiOperation(value = "齐鲁IAM鉴权")
    @RequestMapping(value = "/authorizeWeb/QiluIAM/{apiName}/{dataId}/{ei}/{clientId}/{clientSecret}", method = RequestMethod.GET)
    @ResponseBody
    public Object authorizedIAMQiLu(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei, @RequestHeader("user-agent") String userAgent,
                                    @RequestParam(value = "code", required = false) String code,
                                    @PathVariable("clientId") String clientId,
                                    @PathVariable("clientSecret") String clientSecret,
                                    @RequestParam(value = "grant_type", required = false) String grantType,
                                    HttpServletRequest request) {
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            //获取accessToken
            Result<String> tokenResult = oaUserAuthService.authorizeQiLuByWeb(ei, code, clientId, clientSecret, grantType);
            if(!tokenResult.isSuccess()||StringUtils.isEmpty(tokenResult.getData())){
                log.warn("authorizedIAMQiLu fail, message={}", tokenResult);
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(tokenResult.getErrMsg(),"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei),tokenResult.getData(), apiName, dataId, false, request);
            log.info("QiluIAM urlResultApp:{}", urlResult);
            if(urlResult.isSuccess()){
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
            modelAndView.setViewName(redirectUrl);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return modelAndView;
    }

    @ApiOperation(value = "齐鲁泛微OA代办跳转")
    @RequestMapping(value = "/authorizeWeb/QiLuMessage/{apiName}/{dataId}/{ei}/{clientId}/{clientSecret}", method = RequestMethod.GET)
    @ResponseBody
    public Object QiLuMessage(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei, @RequestHeader("user-agent") String userAgent,
                              @PathVariable("clientId") String clientId,
                              @PathVariable("clientSecret") String clientSecret
    ) {

        Result<OAConnectInfoVO> connectInfo = oaUserAuthService.getConnectInfo(ei);
        if (ObjectUtils.isEmpty(connectInfo)) {
            return ResultCodeEnum.OA_UN_CONNECT.getErrMsg();
        }
        String authUrl = connectInfo.getData().getConnectParams().getSsoAuthUrl();
        //跳转的地址
        /**
         * https://iam-sit-bam.qilu-pharma.com:8073/idp/oauth2/authorize?client_id=IAMDemo&redirect_uri=https://www.baidu.com&response_type=code&state=123
         */
        String formatUrl = "redirect:%s/idp/oauth2/authorize?client_id=%s&redirect_uri=%s&response_type=code&state=qilu";
        String redirectUrl = oaAuthorWebUrl + "QiluIAM/" + apiName + "/" + dataId + "/" + ei + "/" + clientId + "/" + clientSecret;
        String iamUrl = String.format(formatUrl, authUrl, clientId, redirectUrl);
        log.info("formateUrl:{},redirecturl:{},iamUrl:{}", formatUrl, redirectUrl, iamUrl);
        ModelAndView modelAndView = new ModelAndView(iamUrl);
        return modelAndView;
    }

    /**
     * OA免登录重定向至对象详情页
     * @param requestParams
     * @param headers
     * @param apiName
     * @param dataId
     * @param ei
     * @param isApp
     * @return
     */
    @ApiOperation(value = "OA免登录重定向至对象详情页")
    @RequestMapping(value = "/authorize/common/{ei}/{apiName}/{dataId}/{isApp}",method = RequestMethod.GET)
    @ResponseBody
    public Object detailAuth(@RequestParam Map<String,Object> requestParams,
                             @RequestHeader HttpHeaders headers,
                             @PathVariable("apiName") String apiName,
                             @PathVariable("dataId") String dataId,
                             @PathVariable("ei") String ei,
                             @PathVariable("isApp") Boolean isApp, HttpServletRequest request){
        Map<String,Object> params = new HashMap<>();
        requestParams.put("apiName",apiName);
        requestParams.put("dataId",dataId);
        requestParams.put("ei",ei);
        requestParams.put("isApp",isApp);
        params.put("requestParams",requestParams);
        params.put("headers",headers);
        Result<String>  result  = oaUserAuthService.detailAuth(params,ei,request);
        String redirectUrl = "redirect:";
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        if(!result.isSuccess()){
            try {
                if(StringUtils.isNotBlank(ConfigCenter.OA_ERROR_MSG)){
                    result.setErrMsg(ConfigCenter.OA_ERROR_MSG);
                }
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                if(StringUtils.isNotBlank(ConfigCenter.OA_BG_URL)){
                    redirectUrl= redirectUrl.concat("&imgUrl").concat(ConfigCenter.OA_BG_URL);
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }else{
            redirectUrl += result.getData();
        }
        log.info("login url:{}",redirectUrl);
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    @ApiOperation(value = "OA免登录重定向至对象详情页")
    @RequestMapping(value = "/authorize/common/login",method = RequestMethod.GET)
    @ResponseBody
    public Object commonLogin(@RequestParam Map<String,Object> requestParams,
                              @RequestHeader HttpHeaders headers,
                              @RequestHeader("user-agent") String userAgent,
                              HttpServletRequest request){
        Map<String,Object> params = new HashMap<>();
        boolean isApp = false;
        try {
            //useragent不为空且为移动端，isApp为true
            log.info("webhook userAgent:{}", userAgent);
            if (!StringUtils.isEmpty(userAgent)) {
                UserAgent userAgentObj = UserAgent.parseUserAgentString(userAgent);
                OperatingSystem operatingSystem = userAgentObj.getOperatingSystem();
                DeviceType deviceType = operatingSystem.getDeviceType();
                log.info("webhook device:{}", deviceType);
                if (DeviceType.MOBILE == deviceType || DeviceType.TABLET == deviceType) {
                    isApp = true;
                }
            }
        } catch (Exception e) {
            log.info("webhook userAgent error,", e);
        }
        params.put("requestParams",requestParams);
        params.put("headers",headers);
        requestParams.put("isApp",isApp);
        Result<String>  result  = oaUserAuthService.commonRedirectUrl(params,request);
        String redirectUrl = "redirect:";
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        if(!result.isSuccess()){
            try {
                if(StringUtils.isNotBlank(ConfigCenter.OA_ERROR_MSG)){
                    result.setErrMsg(ConfigCenter.OA_ERROR_MSG);
                }
                redirectUrl=String.format(ConfigCenter.ERROR_REDIRECT_URL,URLEncoder.encode(result.getErrMsg(),"UTF-8"), TraceUtil.get());
                if(StringUtils.isNotBlank(ConfigCenter.OA_BG_URL)){
                    redirectUrl= redirectUrl.concat("&imgUrl=").concat(ConfigCenter.OA_BG_URL);
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }else{
            redirectUrl += result.getData();
        }
        log.info("login url:{}",redirectUrl);
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }

    // 目前双胞胎不做鉴权，齐鲁手机端
    @ApiOperation(value = "通用不带验证跳转")
    @RequestMapping(value = "/authorize/avoidlogin/{apiName}/{dataId}/{ei}/{userId}/{isApp}", method = RequestMethod.GET)
    @ResponseBody
    public Object avoidlogin(@PathVariable("apiName") String apiName, @PathVariable("dataId") String dataId, @PathVariable("ei") String ei,
                                  @PathVariable("userId") String userId, @PathVariable("isApp") Object isApp,
                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang,
                             HttpServletRequest request) {

        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        try {
            if (!ConfigCenter.SSO_ENTERPRISE_EI.contains(ei)) {
                String msg = i18NStringManager.get(I18NStringEnum.s3795, lang, ei);
                redirectUrl=String.format(redirectUrl,URLEncoder.encode(msg,"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }

            String userCode = userManager.getFxUserCode(userId, ei);
            if (StringUtils.isEmpty(userCode)) {
                String msg = i18NStringManager.get2(I18NStringEnum.s3796, lang, ei, userId);
                redirectUrl=String.format(redirectUrl,URLEncoder.encode(msg,"UTF-8"), TraceUtil.get());
                modelAndView.setViewName(redirectUrl);
                return modelAndView;
            }
            Result<String> urlResult = userLoginManager.avoidLogin(Integer.valueOf(ei), userCode, apiName, dataId, Boolean.valueOf(isApp.toString()),request);
            log.info("urlResult common:{}", urlResult);
            if (urlResult.isSuccess()) {
                redirectUrl = "redirect:" + urlResult.getData();
            }else {
                redirectUrl=String.format(redirectUrl,URLEncoder.encode(i18NStringManager.getByEi(I18NStringEnum.s1265,ei)+urlResult.getErrMsg(),"UTF-8"), TraceUtil.get());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        modelAndView.setViewName(redirectUrl);
        return modelAndView;
    }
}
