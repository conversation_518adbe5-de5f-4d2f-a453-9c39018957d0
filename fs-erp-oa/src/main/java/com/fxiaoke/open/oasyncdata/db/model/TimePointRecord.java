package com.fxiaoke.open.oasyncdata.db.model;

import cn.hutool.core.lang.Pair;
import lombok.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/2
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimePointRecord {
    private String tenantId;
    private String objApiName;
    private String sourceDataId;
    private String traceId;
    private Long listenTime;
    private Long firstParseTime;
    /**
     * parse前最近的发送mq时间
     */
    private Long lastSendMqTime;
    private Long allFinishTime;
    /**
     * 名称，时间戳
     */
    private LinkedList<SyncDataTimePoint> syncDataTimePoints;


    @Getter
    @Setter
    @ToString
    public static class SyncDataTimePoint {
        private String syncDataId;
        private List<Pair<String, Long>> timePoints = new ArrayList<>();
    }


    public SyncDataTimePoint getLastSyncDataTimePoint() {
        return syncDataTimePoints.isEmpty() ? null : syncDataTimePoints.getLast();
    }

    public void addSyncDataTimePoint() {
        syncDataTimePoints.add(new SyncDataTimePoint());
    }

    public void addTimePoint(String pointName) {
        SyncDataTimePoint lastSyncDataTimePoint = getLastSyncDataTimePoint();
        if (lastSyncDataTimePoint!=null){
            lastSyncDataTimePoint.getTimePoints().add(Pair.of(pointName, System.currentTimeMillis()));
        }
    }

    public void setSyncDataId(String syncDataId) {
        getLastSyncDataTimePoint().setSyncDataId(syncDataId);
    }

}
