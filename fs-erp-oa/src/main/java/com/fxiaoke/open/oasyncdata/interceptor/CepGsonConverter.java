package com.fxiaoke.open.oasyncdata.interceptor;


import com.fxiaoke.open.oasyncdata.model.UserVo;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.json.GsonHttpMessageConverter;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.charset.Charset;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/11
 */
@Slf4j
public class CepGsonConverter extends GsonHttpMessageConverter {
    private Charset getCharset(HttpHeaders headers) {
        if (headers == null || headers.getContentType() == null || headers.getContentType().getCharset() == null) {
            return DEFAULT_CHARSET;
        }
        return headers.getContentType().getCharset();
    }

    @Override
    public Object read(Type type, Class<?> contextClass, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        Reader json = new InputStreamReader(inputMessage.getBody(), getCharset(inputMessage.getHeaders()));
        JsonElement jsonElement = JsonParser.parseReader(json);
        if (jsonElement.isJsonObject()){
            try{
                String currentDcId = jsonElement.getAsJsonObject().get("currentDcId").getAsString();
                if (currentDcId != null) {
                    UserVo userVo = UserContextHolder.get().get();
                    if (userVo != null) {
                        userVo.setDataCenterId(currentDcId);
                    }
                }
            }catch (Exception e){
                log.info("parse currentDcId from json error",e);
            }
        }
        MDC.put("traceId", inputMessage.getHeaders().getFirst("X-fs-Trace-Id"));
        MDC.put("userId", inputMessage.getHeaders().getFirst("X-fs-User-Info"));
        MDC.put("clientInfo", inputMessage.getHeaders().getFirst("X-fs-Client-Info"));
        return getGson().fromJson(jsonElement,type);
    }


    @Override
    protected void writeInternal(Object o, Type type, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        super.writeInternal(o, type, outputMessage);
        MDC.remove("traceId");
        MDC.remove("userId");
        MDC.remove("clientInfo");
    }
}
