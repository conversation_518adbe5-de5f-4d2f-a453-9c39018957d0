package com.fxiaoke.open.oasyncdata.manager;

import cn.hutool.core.date.DateTime;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAObjFieldDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAObjFieldEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.Md5Util;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.model.OARequestModel;
import com.fxiaoke.open.oasyncdata.service.ObjectServiceFiledConvert;
import com.fxiaoke.open.oasyncdata.util.SFAUtils;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * OA字段转换业务
 *
 * <AUTHOR>
 * @date 2021/1/4
 */
@Component("oACommonFieldManager")
@Slf4j
public class OACommonFieldManager {
    @Autowired
    private OAObjFieldDao oaObjFieldDao;

    @Autowired
    private UserManager userManager;

    @ReloadableProperty("oa.author.app.url")
    private String oaAuthorAppUrl;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;
    @Autowired
    private SpringOAUtils springOAUtils;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private MetadataControllerService metadataControllerService;

    /**
     * 转换OA对象字段
     *
     * @param tenantId
     * @param oaSyncApiEntity 同步api
     * @param
     * @return
     */
    public List<OARequestModel> exchangeOAField(String tenantId, String dataCenterId,OASyncApiEntity oaSyncApiEntity, ControllerGetDescribeResult controllerGetDescribeResult, List<Integer> userList) {
        ObjectApiEnum objApiEnumByApiName = ObjectApiEnum.getObjApiEnumByApiName(oaSyncApiEntity.getObjApiName());
        ObjectData objectData = controllerGetDescribeResult.getData();
        //根据不同的biztype获取不同的处理流程
        ObjectServiceFiledConvert approvalService = (ObjectServiceFiledConvert) springOAUtils.getApplicationContext().getBean(objApiEnumByApiName.getServiceName());

        List<OAObjFieldEntity> fieldEntityList = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdAndObj(tenantId, oaSyncApiEntity.getObjApiName());
        approvalService.dealSpecialField(controllerGetDescribeResult, tenantId,dataCenterId);
//        dealSpecialField(data, tenantId);
        String fieldJson = oaSyncApiEntity.getDataTemplate();
        try {
            for (OAObjFieldEntity oaObjFieldEntity : fieldEntityList) {
                String key = oaObjFieldEntity.getReplaceName();
                // 特殊字段处理逻辑
                OAObjectFieldVO oaObjectFieldVO = new OAObjectFieldVO();
                BeanUtils.copyProperties(oaObjFieldEntity, oaObjectFieldVO);
                String specialJson = approvalService.specialHand(oaObjectFieldVO, objectData, tenantId, fieldJson,dataCenterId);
                if (StringUtils.isNotEmpty(specialJson)) {
                    fieldJson = specialJson;
                    if(StringUtils.contains(oaObjFieldEntity.getFieldApiName(),".")) {
                        List<String> items = Splitter.on(".").splitToList(oaObjFieldEntity.getFieldApiName());
                        if(CollectionUtils.isEmpty(items) || items.size() !=3) continue;
                        String objectApiNameKey = items.get(0);
                        String objectDataIdKey = items.get(1);
                        String fieldApiNameKey = items.get(2);
                        ControllerGetDescribeResult detail = SFAUtils.detail(tenantId,
                                objectData.getString(objectDataIdKey),
                                objectData.getString(objectApiNameKey),
                                i18NStringManager, metadataControllerService);
                        if(detail!=null) {
                            String fieldApiNameValue = SFAUtils.getEmpName(objectData,fieldApiNameKey);
                            if(StringUtils.isNotEmpty(fieldApiNameValue)) {
                                fieldJson = fieldJson.replaceAll(key, fieldApiNameValue);
                            }
                        }
                    }
                    continue;
                }
                if (objectData.get(oaObjFieldEntity.getFieldApiName()) == null) {
                    continue;
                }
                String empName = SFAUtils.getEmpName(objectData,oaObjFieldEntity.getFieldApiName());
                if(StringUtils.isNotEmpty(empName)) {
                    fieldJson = fieldJson.replaceAll(key, empName);
                } else {
                    String value = objectData.get(oaObjFieldEntity.getFieldApiName()).toString();
                    fieldJson = fieldJson.replaceAll(key, value);
                }
            }
        } catch (BeansException e) {
            e.printStackTrace();
        }
        //处理最终接受人
        List<String> userCodeList = userList.stream().map(String::valueOf).collect(Collectors.toList());
//        if (userList != null) {
//            userCodeList = userList.stream().map(String::valueOf).collect(Collectors.toList());
//        }

        List<OARequestModel> oaRequestModelList = Lists.newArrayList();
        for (String userCode : userCodeList) {
            OARequestModel oaRequestModel = new OARequestModel();
            oaRequestModel.setReceiverId(userCode);
            String oaUserCode = userManager.getOAUserCode(userCode, tenantId,dataCenterId);
            if (StringUtils.isNotEmpty(oaUserCode)) {
                oaRequestModel.setOaReceiverId(oaUserCode);
            } else {
                oaRequestModel.setOaReceiverId("");
            }
            oaRequestModelList.add(oaRequestModel);
        }
        for (OARequestModel oaRequestModel : oaRequestModelList) {
            String resultJson;
            //审批人，默认应该F001
            resultJson = fieldJson.replaceAll("#F001", oaRequestModel.getOaReceiverId());
            // 处理需要md5加密的字段
            resultJson = exchangeMD5Field(resultJson);
            // 转换时间戳
            resultJson = exchangeDateTimeField(resultJson);

            //处理opinions
            String userCode = oaRequestModel.getReceiverId();
            String oaUserCode = oaRequestModel.getOaReceiverId();
            try {
                if(objectData.get("opinions") != null){
                    List<Map<String, Object>> opinions = (List<Map<String, Object>>) objectData.get("opinions");
                    List<Map<String, Object>> opinion =  opinions.stream().filter(item ->{
                        List<String> replyUser = (List<String>) item.get("reply_user");
                        return (userCode.equals(replyUser.get(0)));
                    }).collect(Collectors.toList());
                    if(opinion.size() == 1){
                        //审批意见
                        if(resultJson.contains("#F060")){
                            resultJson = resultJson.replaceAll("#F060", String.valueOf(opinion.get(0).get("opinion")));
                        }
                        //审批动作
                        if(resultJson.contains("#F061")){
                            resultJson = resultJson.replaceAll("#F061", String.valueOf(opinion.get(0).get("action_type")));
                        }
                        // 审批人
                        if(resultJson.contains("#F062")){
                            resultJson = resultJson.replaceAll("#F062", oaUserCode);
                        }
                        // 审批时间
                        if(resultJson.contains("#F063")){
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Long  replyTimestamp = Double.valueOf(String.valueOf(opinion.get(0).get("reply_time"))).longValue();
                            DateTime dateTime = new DateTime(replyTimestamp);
                            String replyTime =  df.format(dateTime);
                            resultJson = resultJson.replaceAll("#F063",replyTime);
                            resultJson = resultJson.replaceAll("#F064", String.valueOf(replyTimestamp));
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                log.error("exchangeOAField handle opinions error,e:{}",e.getMessage());
            }
            oaRequestModel.setRequestJson(resultJson);
        }
        return oaRequestModelList;
    }

    private String exchangeMD5Field(String resultJson) {
        List<String> md5ValueList = getSubUtil(resultJson, "md5\\((.*?)\\)");
        for (String md5Value : md5ValueList) {
            String value = Md5Util.md5(md5Value).toUpperCase();
            resultJson = resultJson.replaceAll("md5\\(" + md5Value + "\\)", value);
        }
        return resultJson;
    }

    private String exchangeDateTimeField(String resultJson) {
        List<String> dateValueList = getSubUtil(resultJson, "dateTime\\((.*?)\\)");
        for (String dateValue : dateValueList) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long dateValueTime = Long.parseLong(dateValue);
            String format = sdf.format(dateValueTime);
            resultJson = resultJson.replaceAll("dateTime\\(" + dateValue + "\\)", format);
        }
        return resultJson;
    }


    private List<String> getSubUtil(String soap, String rgex) {
        List<String> list = new ArrayList<>();
        Pattern pattern = Pattern.compile(rgex);
        Matcher m = pattern.matcher(soap);
        while (m.find()) {
            list.add(m.group(1));
        }
        return list;
    }


    public List<OARequestModel> exchangeDeleteOAField(String tenantId,String dataCenterId, OASyncApiEntity oaSyncApiEntity, ControllerGetDescribeResult controllerGetDescribeResult,DeleteTodoArg deleteTodoArg) {
        String json = oaSyncApiEntity.getDataTemplate();
        String fieldJson = json.replaceAll("#F012", deleteTodoArg.getSourceId()).replaceAll("#G023",deleteTodoArg.getSourceId()).replaceAll("#currentTime#",
                String.valueOf(System.currentTimeMillis()));
        List<OAObjFieldEntity> fieldEntityList = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdAndObj(tenantId, oaSyncApiEntity.getObjApiName());
        ObjectApiEnum objApiEnumByApiName = ObjectApiEnum.getObjApiEnumByApiName(oaSyncApiEntity.getObjApiName());
        //根据不同的biztype获取不同的处理流程
        ObjectServiceFiledConvert approvalService = (ObjectServiceFiledConvert) springOAUtils.getApplicationContext().getBean(objApiEnumByApiName.getServiceName());
        approvalService.dealSpecialField(controllerGetDescribeResult, tenantId,dataCenterId);
        ObjectData objectData = controllerGetDescribeResult.getData();
        try {
            for (OAObjFieldEntity oaObjFieldEntity : fieldEntityList) {
                String key = oaObjFieldEntity.getReplaceName();
//                // 特殊字段处理逻辑
                OAObjectFieldVO oaObjectFieldVO = new OAObjectFieldVO();
                BeanUtils.copyProperties(oaObjFieldEntity, oaObjectFieldVO);
                String specialJson = approvalService.specialHand(oaObjectFieldVO, objectData, tenantId, fieldJson,dataCenterId);
                if (StringUtils.isNotEmpty(specialJson)) {
                    fieldJson = specialJson;
                    continue;
                }
                if (objectData.get(oaObjFieldEntity.getFieldApiName()) == null) {
                    continue;
                }
                String value = objectData.get(oaObjFieldEntity.getFieldApiName()).toString();
                fieldJson = fieldJson.replaceAll(key, value);
            }
        } catch (BeansException e) {
            e.printStackTrace();
        }
        List<String> userCodeList = deleteTodoArg.getDeleteEmployeeIds().stream().map(String::valueOf).collect(Collectors.toList());
        List<String> erpUserCodeList = Lists.newArrayList();
        List<OARequestModel> oaRequestModelList = Lists.newArrayList();
        for (String userCode : userCodeList) {
            OARequestModel oaRequestModel = new OARequestModel();
            oaRequestModel.setReceiverId(userCode);
            String erpUserCode = userManager.getOAUserCode(userCode, tenantId,dataCenterId);
            if (StringUtils.isNotEmpty(erpUserCode)) {
                oaRequestModel.setOaReceiverId(erpUserCode);
            } else {
                oaRequestModel.setOaReceiverId("");
            }
            oaRequestModelList.add(oaRequestModel);
        }
        String resultJson;
        for (OARequestModel oaRequestModel : oaRequestModelList) {
            resultJson = fieldJson.replaceAll("#F001", oaRequestModel.getOaReceiverId());
            // 处理需要md5加密的字段
            resultJson = exchangeMD5Field(resultJson);
            // 转换时间戳
            resultJson = exchangeDateTimeField(resultJson);
            oaRequestModel.setRequestJson(resultJson);
        }
        return oaRequestModelList;
    }

    public void exchangeLayoutField(CreateTodoArg createTodoArg, OARequestModel oaRequestModel) {
        List<KeyValueItem> keyValueItems = createTodoArg.getForm();
        String json = oaRequestModel.getRequestJson();
        StringBuffer formField = new StringBuffer();
        for (KeyValueItem keyValueItem : keyValueItems) {
            formField.append(keyValueItem.getKey() + ":" + keyValueItem.getValue());
            formField.append("\\\\n");
        }
        json = json.replaceAll("#F057", formField.toString());
        oaRequestModel.setRequestJson(json);
    }

    //不同type换取不同的title
    public Map<String,String> builderTitle(ObjectData objectData,OASyncApiEntity oaSyncApiEntity,String bizType,String dataCenterId){
        ObjectApiEnum objApiEnumByApiName = ObjectApiEnum.getObjApiEnumByApiName(oaSyncApiEntity.getObjApiName());
        ObjectServiceFiledConvert approvalService = (ObjectServiceFiledConvert) springOAUtils.getApplicationContext().getBean(objApiEnumByApiName.getServiceName());
        Map<String, String> nameMap = approvalService.builderTitle(objectData,bizType,oaSyncApiEntity.getTenantId(),dataCenterId);
        return nameMap;

    }

    public List<OARequestModel> generateExpressionRequest(String tenantId,String dataCenterId,Map<String,Object> paramsMap,OASyncApiEntity oaSyncApiEntity,List<String> userIds){
        List<OAObjFieldEntity> fieldEntityList = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdAndObj(tenantId, oaSyncApiEntity.getObjApiName());
        String fieldJson = oaSyncApiEntity.getDataTemplate();
        try {
            for (OAObjFieldEntity oaObjFieldEntity : fieldEntityList) {
                String key = oaObjFieldEntity.getReplaceName();
                OAObjectFieldVO oaObjectFieldVO = new OAObjectFieldVO();
                BeanUtils.copyProperties(oaObjFieldEntity, oaObjectFieldVO);
                if (paramsMap.get(oaObjFieldEntity.getFieldApiName()) == null) {
                    continue;
                }
                String value = paramsMap.get(oaObjFieldEntity.getFieldApiName()).toString();
                if(key.equals("#H010")){
                    //H010的数据有转义的字符，用replaceall会去除转义，反序列有问题，先处理一下H010
                    fieldJson = fieldJson.replace(key, value);
                }else{
                    fieldJson = fieldJson.replaceAll(key, value);
                }

            }
        } catch (BeansException e) {
            e.printStackTrace();
        }
        //处理最终接受人
        List<OARequestModel> oaRequestModelList = Lists.newArrayList();
        for (String userCode : userIds) {
            OARequestModel oaRequestModel = new OARequestModel();
            oaRequestModel.setReceiverId(userCode);
            String oaUserCode = userManager.getOAUserCode(userCode, tenantId,dataCenterId);
            if (StringUtils.isNotEmpty(oaUserCode)) {
                oaRequestModel.setOaReceiverId(oaUserCode);
            } else {
                oaRequestModel.setOaReceiverId("");
            }
            String requestJson=fieldJson.replaceAll("#H001",oaRequestModel.getOaReceiverId());
            oaRequestModel.setRequestJson(requestJson);
            oaRequestModelList.add(oaRequestModel);
        }
        return oaRequestModelList;
    }


}
