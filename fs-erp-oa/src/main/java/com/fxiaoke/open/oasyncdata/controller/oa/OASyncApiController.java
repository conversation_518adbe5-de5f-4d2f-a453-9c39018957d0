package com.fxiaoke.open.oasyncdata.controller.oa;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.arg.CepCrmTypeArg;
import com.fxiaoke.open.oasyncdata.arg.CepListArg;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.service.NotificationService;
import com.fxiaoke.open.oasyncdata.service.OASyncApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA策略参数相关接口")
@RestController("oaSyncApiController")
@RequestMapping("cep/oa/syncApi")
@Slf4j
public class OASyncApiController extends BaseController {
    @Autowired
    private OASyncApiService oaSyncApiService;
    @Autowired
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;

    @ApiOperation(value = "获取企业对应事件的所有策略")
    @RequestMapping(value = "/getOASyncApi", method = RequestMethod.POST)
    public Result<List<OASyncApiVO>> getOASyncApiList(
            @ApiParam(name = "eventType",value = "事件类型1新增 2处理 3删除") @RequestBody CepCrmTypeArg cepCrmTypeArg) {
        String tenantId = getLoginUserTenantId();
        List<ObjectApiEnum> businessEnumByType = ObjectApiEnum.getBusinessEnumByType(cepCrmTypeArg.getCrmType());
        List<String> objByCrmType=businessEnumByType.stream().map(item ->item.getObjApiName()).collect(Collectors.toList());
        return oaSyncApiService.getOASyncApiList(tenantId,null,objByCrmType,cepCrmTypeArg.getCurrentDcId());
    }

    @ApiOperation(value = "获取企业事件开关")
    @RequestMapping(value = "/getOASyncStatus", method = RequestMethod.POST)
    public Result<Map<String,Integer>> getOASyncStatus(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        return oaSyncApiService.getOpenApiStatus(tenantId,arg.getCurrentDcId());
    }


    @ApiOperation(value = "更新事件状态")
    @RequestMapping(value = "/updateEventStatus", method = RequestMethod.POST)
    public Result<Integer> updateOASyncApi(@RequestBody OASyncApiSettingVo oaSyncApiVO) {
        String tenantId = getLoginUserTenantId();
        return oaSyncApiService.updateOaApiStatus(oaSyncApiVO, tenantId,oaSyncApiVO.getCurrentDcId());
    }

    @ApiOperation(value = "保存OA流程的具体信息，包括删除，删除的status传3")
    @RequestMapping(value = "/updateApiInfo", method = RequestMethod.POST)
    public Result<String> updateOASyncApi(@RequestBody CepListArg<OASyncApiVO> arg) {
        String tenantId = getLoginUserTenantId();
        return oaSyncApiService.updateOASyncApi(arg.getCepListArg(), tenantId,arg.getCurrentDcId());
    }

    @ApiOperation(value = "返回业务场景的类型")
    @RequestMapping(value = "/getBusinessType", method = RequestMethod.POST)
    public Result<List<OAMessageTypeVO>> getBusinessType(@RequestBody BusinessTypeArg arg,
                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return oaSyncApiService.getBusinessType(tenantId,arg.getType(),arg.getCurrentDcId(),lang);
    }

    @ApiOperation(value = "更新待办对应的自定义函数")
    @RequestMapping(value = "/updateTodoCustomFunc", method = RequestMethod.POST)
    public Result<Void> updateTodoCustomFunc(@RequestBody BindFuncArg arg) {
        String tenantId = getLoginUserTenantId();
        return oaSyncApiService.updateTodoCustomFunc(tenantId,arg.getApiName(),arg.getCurrentDcId());
    }

    @ApiOperation(value = "获取导入模板")
    @RequestMapping(value = "/getTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getTemplate(
            @RequestBody ImportExcelFile.FieldDataMappingArg arg,
            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String ea = getEa();
        arg.setDataCenterId(getDcId());
        return fileService.buildExcelTemplate(ea, arg, lang);
    }
}
