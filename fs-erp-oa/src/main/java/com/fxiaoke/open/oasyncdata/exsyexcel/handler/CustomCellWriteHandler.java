package com.fxiaoke.open.oasyncdata.exsyexcel.handler;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

public class CustomCellWriteHandler implements CellWriteHandler {
    private I18NStringManager i18NStringManager;
    private String tenantId;
    private String lang;

    public CustomCellWriteHandler(I18NStringManager i18NStringManager, String tenantId, String lang) {
        this.i18NStringManager = i18NStringManager;
        this.tenantId = tenantId;
        this.lang = lang;
    }

    public void init(String tenantId, String lang) {
        this.tenantId = tenantId;
        this.lang = lang;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 Row row,
                                 Head head,
                                 Integer integer,
                                 Integer integer1,
                                 Boolean aBoolean) {
        if(head!=null) {
            List<String> headNameList = head.getHeadNameList();
            if(CollectionUtils.isNotEmpty(headNameList)) {
                String i18nKey = headNameList.get(0);
                headNameList.set(0,i18NStringManager.get(i18nKey,lang,tenantId,i18nKey));
            }
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                WriteTableHolder writeTableHolder,
                                Cell cell,
                                Head head,
                                Integer integer,
                                Boolean aBoolean) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
                                       WriteTableHolder writeTableHolder,
                                       CellData cellData,
                                       Cell cell,
                                       Head head,
                                       Integer integer,
                                       Boolean aBoolean) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 List<CellData> list,
                                 Cell cell,
                                 Head head,
                                 Integer integer,
                                 Boolean aBoolean) {

    }
}
