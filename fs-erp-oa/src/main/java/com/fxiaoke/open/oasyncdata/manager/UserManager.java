package com.fxiaoke.open.oasyncdata.manager;


import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户对象业务
 *
 * <AUTHOR>
 * @date 2021/1/4
 */
@Component("userManager")
@Slf4j
public class UserManager {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    /**
     * 用户对象code列表转换
     *
     * @param receiverIds
     * @param tenantId
     * @return
     */
    public List<String> getUserCodeList(List<String> receiverIds, String tenantId,String dataCenterId) {
        if (CollectionUtils.isEmpty(receiverIds)) {
            log.info("getUserCode receiverIds is null");
            return new ArrayList<>();
        }
        return erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryOAUserCodeList(receiverIds, tenantId,dataCenterId);
    }

    /**
     * 用户对象查询OAcode转换
     *
     * @param receiverId
     * @param tenantId
     * @return
     */
    public String getOAUserCode(String receiverId, String tenantId,String dataCenterId) {
        if (StringUtils.isEmpty(receiverId)) {
            log.info("getUserCode receiverIds is null");
            return null;
        }
        return erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryOAUserCode(receiverId, tenantId,dataCenterId);
    }

    /**
     * 用户对象查询Fxcode转换
     *
     * @param receiverId
     * @param tenantId
     * @return
     */
    public String getFxUserCode(String receiverId, String tenantId) {
        if (StringUtils.isEmpty(receiverId)) {
            log.info("getUserCode receiverIds is null");
            return null;
        }
        return erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryFxUserCode(receiverId, tenantId,null);
    }

    /**
     * oaAuth的方法太多，多数据中心暂时只有给commonlogin
     * @param receiverId
     * @param tenantId
     * @param dataCenterId
     * @return
     */
    public String getFxUserCodeByDataCenterId(String receiverId, String tenantId,String dataCenterId) {
        if (StringUtils.isEmpty(receiverId)) {
            log.info("getUserCode receiverIds is null");
            return null;
        }
        return erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryFxUserCode(receiverId, tenantId,dataCenterId);
    }
}
