package com.fxiaoke.open.oasyncdata.model;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OAInterfaceMonitorLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erp-oa-interface-monitor";
    @Tag(2)
    private long stamp;
    @Tag(3)
    @Builder.Default
    private String appName="fs-erp-oa";
    @Tag(7)
    private String tenantId;
    @Tag(9)
    private String traceId;

    @Tag(51)
    private String url;
    @Tag(52)
    private String params;
    @Tag(53)
    private String header;
    @Tag(54)
    private String requestMode;
    @Tag(55)
    private String exceptionType;
    @Tag(56)
    private String exceptionMsg;
    @Tag(57)
    private String httpResponse;

    @Tag(151)
    private Integer status;

    @Tag(201)
    private Long callTime;
    @Tag(202)
    private Long returnTime;
    @Tag(203)
    private Long costTime;

}
