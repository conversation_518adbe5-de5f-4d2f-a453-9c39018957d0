package com.fxiaoke.open.oasyncdata.model;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OADealWithMsgLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erp-oa-deal-msg";
    @Tag(2)
    private long stamp;
    @Tag(3)
    @Builder.Default
    private String appName="fs-erp-oa";
    @Tag(7)
    private String tenantId;
    @Tag(9)
    private String traceId;

    @Tag(51)
    private String mqType;
    @Tag(52)
    private String operateType;

}
