package com.fxiaoke.open.oasyncdata.impl;

import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.BaseArg;
import com.fxiaoke.open.oasyncdata.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.PageArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.EmployeeMappingService;
import com.fxiaoke.open.oasyncdata.service.FsObjectDataService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date: 10:03 2020/9/3
 * @Desc:
 */
@Slf4j
@Service
@Data
public class EmployeeMappingServiceImpl implements EmployeeMappingService {

    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Override
    public Result<String> updateOAEmployeeMappingByErpId(String tenantId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setTenantId(tenantId);
        erpFieldDataMappingEntity.setDataCenterId(employeeMappingResult.getCurrentDcId());
        erpFieldDataMappingEntity.setChannel(employeeMappingResult.getChannel());
        String erpEmployeeId = employeeMappingResult.getErpEmployeeId();
        erpFieldDataMappingEntity.setErpDataId(erpEmployeeId);
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee_oa);
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        List<ErpFieldDataMappingEntity> oldMapping = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch(tenantId,employeeMappingResult.getCurrentDcId(),ErpFieldTypeEnum.employee_oa,null,erpEmployeeId);
        if(CollectionUtils.isEmpty(oldMapping)) {
            oldMapping = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listNoSearch(tenantId,employeeMappingResult.getCurrentDcId(),ErpFieldTypeEnum.employee_oa,erpFieldDataMappingEntity.getFsDataId(),null);
        }
        if (CollectionUtils.isNotEmpty(oldMapping) && oldMapping.size() == 1) {//只有一个相同，更新
            erpFieldDataMappingEntity.setId(oldMapping.get(0).getId());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                Result result = Result.newSuccess();
                result.setData("update");
                return result;
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        } else {//0个或者多个，删除再插入
            if (CollectionUtils.isNotEmpty(oldMapping)) {//多个
                for (ErpFieldDataMappingEntity entity : oldMapping) {
                    erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(entity.getId());
                }
            }
            erpFieldDataMappingEntity.setId(idGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult == 1) {
                Result result = Result.newSuccess();
                result.setData("add");
                return result;
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        }
    }





}
