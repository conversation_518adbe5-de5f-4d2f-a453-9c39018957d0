package com.fxiaoke.open.oasyncdata.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.*;

public class JsonDataUtils {

    private static ArrayList<String> strArrayList = new ArrayList<String> ();
    private static ArrayList<String> strArrayMap = new ArrayList<String> ();
    /**
     * 将json字符串转为Map结构
     * 如果json复杂，结果可能是map嵌套map
     * @param jsonStr 入参，json格式字符串
     * @return 返回一个map
     */
    public static Map<String, Object> json2Map(String jsonStr) {
        Map<String, Object> map = new LinkedHashMap<>();
        if(jsonStr != null && !"".equals(jsonStr)){
            //最外层解析
            JSONObject json = JSONObject.parseObject(jsonStr);
            for (Object k : json.keySet()) {
                Object v = json.get(k);
                //如果内层还是数组的话，继续解析
                if (v instanceof JSONArray) {
                    List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                    Iterator<Object> it = ((JSONArray) v).iterator();
                    while (it.hasNext()) {
                        Object tempObj = it.next();
                        JSONObject tempJO = (JSONObject)tempObj;
                        list.add(json2Map(tempJO.toString()));
                    }
                    map.put(k.toString(), list);
                } else {
                    map.put(k.toString(), v);
                }
            }
            return map;
        }else{
            return null;
        }
    }

    public static List<Map> json2ListMap(String jsonStr){
        List<Map> retList = new ArrayList<>();
        if(jsonStr != null && !"".equals(jsonStr)){
            JSONArray objects = JSONObject.parseArray(jsonStr);
            if(objects != null && objects.size() >0){
                for (Object tempOpj:objects) {
                    Map<String, Object> objectMap = json2Map(String.valueOf(tempOpj));
                    retList.add(objectMap);
                }
            }
        }
        return retList;
    }
}
