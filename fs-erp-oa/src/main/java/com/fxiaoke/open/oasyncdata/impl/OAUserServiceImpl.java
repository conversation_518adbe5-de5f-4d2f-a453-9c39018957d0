package com.fxiaoke.open.oasyncdata.impl;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.BuildExcelFile;
import com.fxiaoke.open.oasyncdata.model.OAEmployeeDataMappingExcelVo;
import com.fxiaoke.open.oasyncdata.model.QueryOAEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.service.NotificationService;
import com.fxiaoke.open.oasyncdata.service.OAUserService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:OA用户绑定操作
 */
@Service
@Slf4j
@Data
public class OAUserServiceImpl implements OAUserService {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;


    @Override
    public Result<QueryResult<List<EmployeeMappingResult>>> getOAUserList(String tenantId, QueryOAEmployeeMappingListArg pageArg) {
        log.info("OAUserServiceImpl.getOAUserList,tenantId={},pageArg={}",tenantId,pageArg);
        QueryResult<List<EmployeeMappingResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(pageArg.getPageNum());
        queryResult.setPageSize(pageArg.getPageSize());
        String queryStr = null;
        if (StringUtils.isNotEmpty(pageArg.getQueryStr())) {
            queryStr = "%" + pageArg.getQueryStr() + "%";
        }
        ErpFieldDataMappingEntity arg = new ErpFieldDataMappingEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(pageArg.getCurrentDcId());
        arg.setDataType(ErpFieldTypeEnum.employee_oa);
        Integer total = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).countByTenantIdAndDataType(tenantId, pageArg.getCurrentDcId(),ErpFieldTypeEnum.employee_oa, queryStr);
        queryResult.setTotal(total);
        log.info("OAUserServiceImpl.getOAUserList,total={}",total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndDataType(tenantId, pageArg.getCurrentDcId(),ErpFieldTypeEnum.employee_oa, queryResult.getPageSize(),
                        (pageArg.getPageNum() - 1) * pageArg.getPageSize(), queryStr);
        log.info("OAUserServiceImpl.getOAUserList,erpFieldDataMappingEntities={}",erpFieldDataMappingEntities);
        if (CollectionUtils.isEmpty(erpFieldDataMappingEntities)) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<EmployeeMappingResult> employeeMappingResults = Lists.newArrayList();
        for (ErpFieldDataMappingEntity erpFieldDataMappingEntity : erpFieldDataMappingEntities) {
            EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
            employeeMappingResult.setErpEmployeeId(erpFieldDataMappingEntity.getErpDataId());
            employeeMappingResult.setFsEmployeeId(Integer.valueOf(erpFieldDataMappingEntity.getFsDataId()));
            employeeMappingResult.setFsEmployeeName(erpFieldDataMappingEntity.getFsDataName());
            employeeMappingResult.setErpEmployeeName(erpFieldDataMappingEntity.getErpDataName());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            employeeMappingResults.add(employeeMappingResult);
        }
        queryResult.setDataList(employeeMappingResults);
        log.info("OAUserServiceImpl.getOAUserList,queryResult={}",queryResult);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<String> updateOAUserInfo(String tenantId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setChannel(ErpChannelEnum.OA);
        erpFieldDataMappingEntity.setDataCenterId(employeeMappingResult.getCurrentDcId());
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee_oa);
        erpFieldDataMappingEntity.setId(employeeMappingResult.getId());
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataId(employeeMappingResult.getErpEmployeeId());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        erpFieldDataMappingEntity.setTenantId(tenantId);
        if (StringUtils.isEmpty(employeeMappingResult.getId())) {//插入
            Result<String> ifExist = ifExist(erpFieldDataMappingEntity);
            if (!ifExist.isSuccess()) {
                return ifExist;
            }
            erpFieldDataMappingEntity.setId(idGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult == 1) {
                Result result = Result.newSuccess();
                result.setData("add");
                return result;
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        } else {//更新
            Result<String> ifExist = ifExist(erpFieldDataMappingEntity);
            if (!ifExist.isSuccess()) {
                return ifExist;
            }
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                Result result = Result.newSuccess();
                result.setData("update");
                return result;
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        }
    }



    @Override
    public Result<String> deleteOAUserInfo(String tenantId, EmployeeMappingResult employeeMappingResult) {
        int num = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, employeeMappingResult.getId());
        if (num > 0) {
            return Result.newSuccess();
        } else {
            return Result.newError(ResultCodeEnum.DATA_NOT_FOUND);
        }
    }

    @Override
    public Result<String> batchDeleteOAUserMapping(String tenantId,
                                                   String dataCenterId,
                                                   List<String> idList,
                                                   String lang) {
        log.info("OAUserServiceImpl.batchDeleteOAUserMapping,tenantId={},idList={}", tenantId, idList);
        int count = 0;
        if (CollectionUtils.isEmpty(idList)) {
            count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .deleteByDataType(tenantId, dataCenterId, ErpFieldTypeEnum.employee_oa);
        } else {
            count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .batchDeleteByIds(tenantId, idList);
        }
        return Result.newSuccess(count + "");
    }

    @Override
    public Result<Void> exportOAUserMapping(String tenantId,
                                            Integer userId,
                                            String dataCenterId,
                                            List<String> idList,
                                            String lang) {
        List<ErpFieldDataMappingEntity> entityList = null;
        if (CollectionUtils.isEmpty(idList)) {
            entityList = erpFieldDataMappingDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listNoSearch(tenantId, dataCenterId, ErpFieldTypeEnum.employee_oa, null, null);
        } else {
            entityList = erpFieldDataMappingDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByIdList(tenantId, dataCenterId, idList);
        }

        List<OAEmployeeDataMappingExcelVo> dataMappingExcelVoList = new ArrayList<>();
        for (ErpFieldDataMappingEntity entity : entityList) {
            OAEmployeeDataMappingExcelVo excelVo = new OAEmployeeDataMappingExcelVo();
            excelVo.setCrmEmployeeId(entity.getFsDataId());
            excelVo.setOaEmployeeId(entity.getErpDataId());
            excelVo.setOaEmployeeName(entity.getErpDataName());
            dataMappingExcelVoList.add(excelVo);
        }

        String dataTypeName = i18NStringManager.get(I18NStringEnum.s353, lang, tenantId);

        BuildExcelFile.Arg<OAEmployeeDataMappingExcelVo> excelFileArg = new BuildExcelFile.Arg<>();
        excelFileArg.setTenantId(tenantId);
        excelFileArg.setDataList(dataMappingExcelVoList);
        excelFileArg.setFileName(dataTypeName + i18NStringManager.get2(I18NStringEnum.s408.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s408.getI18nValue(), tenantId, LocalDateTime.now().toString()),
                Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
        excelFileArg.setSheetNames(Collections.singletonList(dataTypeName));

        Result<BuildExcelFile.Result> resultResult = fileService.buildExcelFile(excelFileArg, lang);

        if (resultResult.isSuccess()) {
            sendExportResult(tenantId, dataCenterId, userId, dataTypeName, resultResult, lang);
        }

        return Result.newSuccess();
    }

    private void sendExportResult(String tenantId,
                                  String dataCenterId,
                                  Integer userId,
                                  String dataTypeName,
                                  Result<BuildExcelFile.Result> excelFileResult,
                                  String lang) {
        dataTypeName += i18NStringManager.get(I18NStringEnum.s403,lang,tenantId);
        String msg = excelFileResult.getErrMsg();
        if (excelFileResult.isSuccess()) {
            String downloadUrl = String.format(ConfigCenter.DOWNLOAD_FILE_PATH, excelFileResult.getData().getTnFilePath() + ".xlsx", URLEncoder.encode(dataTypeName) + ".xlsx");
            msg = i18NStringManager.get(I18NStringEnum.s404,lang,tenantId) + downloadUrl;
        }
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        //sendTextNoticeArg.setDataCenterId(dataCenterId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(dataTypeName + i18NStringManager.get(I18NStringEnum.s405,lang,tenantId) + LocalDateTime.now().toString());
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg);
    }

    private Result<String> ifExist(ErpFieldDataMappingEntity erpFieldDataMappingEntity) {
        String tenantId = erpFieldDataMappingEntity.getTenantId();
        String fsDataId = erpFieldDataMappingEntity.getFsDataId();
        String erpDataId = erpFieldDataMappingEntity.getErpDataId();
        ErpFieldTypeEnum dataType = erpFieldDataMappingEntity.getDataType();
        if (StringUtils.isNotEmpty(fsDataId)) {//如果不为空，校验fs的dataId是否唯一
            List<ErpFieldDataMappingEntity> oldEntry = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, erpFieldDataMappingEntity.getDataCenterId(), dataType, fsDataId, null);
            if (CollectionUtils.isNotEmpty(oldEntry) && !oldEntry.get(0).getId().equals(erpFieldDataMappingEntity.getId())) {
                Result result = Result.newError(ResultCodeEnum.THE_CRM_EMPLOYEE_MAPPING_EXIST);
                return result;
            }
        }
        if (StringUtils.isNotEmpty(erpDataId)) {//如果不为空，校验erp的dataId是否唯一
            List<ErpFieldDataMappingEntity> oldEntry1 = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, erpFieldDataMappingEntity.getDataCenterId(), dataType, null, erpDataId);
            if (CollectionUtils.isNotEmpty(oldEntry1) && !oldEntry1.get(0).getId().equals(erpFieldDataMappingEntity.getId())) {
                return Result.newError(ResultCodeEnum.THE_ERP_EMPLOYEE_MAPPING_EXIST);
            }
        }
        return Result.newSuccess();
    }
}
