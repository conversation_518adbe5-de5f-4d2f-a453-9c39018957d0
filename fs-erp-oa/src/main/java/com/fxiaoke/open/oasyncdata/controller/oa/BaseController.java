package com.fxiaoke.open.oasyncdata.controller.oa;


import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.oasyncdata.interceptor.UserContextHolder;
import com.fxiaoke.open.oasyncdata.model.UserVo;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.Objects;

public class BaseController {
    @Autowired
    private EnterpriseEditionService enterpriseEditionServiceSelf;
    protected String getEa(){
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return userVo.getEnterpriseAccount();
    }

    protected Integer getIntLoginUserTenantId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return userVo.getEnterpriseId();
    }

    protected String getPhone() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return userVo.getMobile();
    }

    protected String getLoginUserTenantId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return String.valueOf(userVo.getEnterpriseId());
    }

    protected Integer getLoginUserId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEmployeeId())){
            return null;
        }
        return userVo.getEmployeeId();
    }


    public UserVo getUserVo() {
        return UserContextHolder.get().get();
    }

    public String getEnterpriseName(){
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        SimpleEnterprise simpleEnterprise = new SimpleEnterprise();
        GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
        getSimpleEnterpriseArg.setEnterpriseAccount(userVo.getEnterpriseAccount());
        getSimpleEnterpriseArg.setEnterpriseId(userVo.getEnterpriseId());
        GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionServiceSelf.getSimpleEnterprise(getSimpleEnterpriseArg);
        simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
        if (Objects.isNull(simpleEnterprise)||Objects.isNull(simpleEnterprise.getEnterpriseName())){
            return null;
        }
        return simpleEnterprise.getEnterpriseName();
    }
    protected String getDcId(){
        UserVo userVo = getUserVo();
        if (userVo==null){
            throw new ErpSyncDataException(I18NStringEnum.s115,null);
        }
        String dataCenterId = userVo.getDataCenterId();
        if (StringUtils.isBlank(dataCenterId)){
            throw new ErpSyncDataException(I18NStringEnum.s116,userVo.getEnterpriseId()+"");
        }
        return dataCenterId;
    }
}
