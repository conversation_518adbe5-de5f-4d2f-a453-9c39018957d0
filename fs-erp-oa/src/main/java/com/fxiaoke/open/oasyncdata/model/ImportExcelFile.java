package com.fxiaoke.open.oasyncdata.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.ExcelTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/31
 */
public class ImportExcelFile {

    @Data
    @ApiModel("基础字段数据映射导入参数")
    @ToString(exclude = "file")
    public static class FieldDataMappingArg implements Serializable {
        private static final long serialVersionUID = -8761645379239188525L;
        /**
         * 企业id
         */
        @NotNull
        private String tenantId;

        /**
         * 用户id
         */
        private Integer userId;
        @NotNull
        private String dataCenterId;

        @ApiModelProperty(value = "模板类型")
        @NotNull(message = "模板类型不能为空")
        private ExcelTypeEnum excelType;

        /**
         * @see com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum
         */
        @ApiModelProperty(value = "数据类型,或者对象apiName")
        @NotNull(message = "数据类型不能为空")
        private ErpFieldTypeEnum dataType;

        @ApiModelProperty(value = "文件流")
        @JSONField(serialize = false)
        private MultipartFile file;

        @ApiModelProperty(value = "文件流")
        private InputStream fileStream;

        @ApiModelProperty(value = "文件类型,XLSX/XLS")
        private String fileType;

        /**
         * 当前语言
         */
        private String lang;
    }

    @Data
    @ApiModel("获取对象数据映射模板参数")
    @ToString(exclude = "file")
    public static class ObjectDataMappingTemplateArg implements Serializable {
        /**
         * 企业id
         */
        @NotNull
        private String tenantId;

        @ApiModelProperty(value = "策略id")
        @NotNull(message = "策略id不能为空")
        private String ployDetailId;

    }

    @Data
    @ApiModel("对象数据映射导入参数")
    @ToString(exclude = "file")
    public static class ObjectDataMappingArg implements Serializable {
        private static final long serialVersionUID = -3127337895746409542L;
        /**
         * 企业id
         */
        @NotNull
        private String tenantId;

        /**
         * 操作人
         */
        @NotNull
        private Integer userId;

        @ApiModelProperty(value = "模板类型")
        @NotNull(message = "模板类型不能为空")
        private ExcelTypeEnum excelType;

        @ApiModelProperty(value = "策略明细id")
        @NotNull(message = "策略明细id不能为空")
        private String ployDetailId;

        @ApiModelProperty(value = "文件流")
        private MultipartFile file;
    }

    @Data
    @ApiModel("导入excel文件结果")
    public static class Result implements Serializable {
        private static final long serialVersionUID = 9181946463285972887L;

        @ApiModelProperty(value = "解析成功行数")
        private Integer invokedNum;

        @ApiModelProperty(value = "插入数据行数")
        private Integer insertNum;

        @ApiModelProperty(value = "更新数据行数")
        private Integer updateNum;

        @ApiModelProperty(value = "删除数据行数")
        private Integer deleteNum;

        @ApiModelProperty(value = "数据不合法行信息,该信息汇总展示")
        private List<ErrorRow> importErrorRows;

        @ApiModelProperty(value = "解析失败行信息，该信息每条分别展示")
        private List<ErrorRow> invokeExceptionRows;


        @ApiModelProperty(value = "汇总信息")
        private String printMsg= StringUtils.EMPTY;

        @ApiModelProperty(value = "前端使用的汇总信息")
        private String message;

        public Result() {
            invokedNum = 0;
            insertNum = 0;
            updateNum = 0;
            deleteNum = 0;
            importErrorRows = new ArrayList<>();
            invokeExceptionRows = new ArrayList<>();
        }

        public void setPrintMsg(String printMsg) {
            this.printMsg = printMsg;
            message = printMsg;
        }

        public void incrDelete(int incr) {
            deleteNum+=incr;
        }

        public void incrInsert(int incr) {
            insertNum+=incr;
        }

        public void incrUpdate(int incr) {
            updateNum+=incr;
        }

        public void incrInvoke(int incr) {
            invokedNum+=incr;
        }

        public void addImportError(Integer rowNo, String errMsg) {
            ErrorRow errorRow = new ErrorRow();
            errorRow.setRowNo(rowNo);
            errorRow.setErrMsg(errMsg);
            this.importErrorRows.add(errorRow);
        }

        public void addInvokeExceptionRow(Integer rowNo, String errMsg) {
            ErrorRow errorRow = new ErrorRow();
            errorRow.setRowNo(rowNo);
            errorRow.setErrMsg(errMsg);
            this.importErrorRows.add(errorRow);
        }
    }

    @Data
    @ApiModel("导入excel文件错误结果行")
    public static class ErrorRow implements Serializable {
        private static final long serialVersionUID = 3968251167626669331L;
        @ApiModelProperty(value = "行号")
        private Integer rowNo;
        @ApiModelProperty(value = "错误信息")
        private String errMsg;
    }

    @Data
    @ApiModel("对象数据映射导入参数")
    @ToString(exclude = "file")
    public static class FieldImportArg implements Serializable {
        private static final long serialVersionUID = -3127337895746409542L;
        /**
         * 企业id
         */
        @NotNull
        private String tenantId;

        /**
         * 数据中心ID
         */
        @NotNull
        private String dataCenterId;

        /**
         * 操作人
         */
        @NotNull
        private Integer userId;

        /**
         * ERP真实对象apiName
         */
        @NotNull
        private String erpRealObjectApiName;

        @ApiModelProperty(value = "模板类型")
        @NotNull(message = "模板类型不能为空")
        private ExcelTypeEnum excelType;

        @ApiModelProperty(value = "文件流")
        private MultipartFile file;
    }

    @Data
    @ApiModel("字段导入结果")
    public static class FieldImportResult {
        /**
         * 导入成功条数
         */
        private int successCount;
        /**
         * 导入失败条数
         */
        private int failedCount;

        public void increaseSuccess() {
            successCount++;
        }

        public void increaseFailed() {
            failedCount++;
        }
    }
}
