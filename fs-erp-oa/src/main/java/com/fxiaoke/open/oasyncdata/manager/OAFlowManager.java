package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.FlowEvetTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OAEventEnum;
import com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAFlowMqConfigDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.util.FunctionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OAFlowManager {

    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;
    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    FunctionUtils functionUtils;

    @Autowired
    private MetadataControllerService metadataControllerService;

    @Autowired
    private OAFlowMqConfigDao oaFlowMqConfigDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    public void handle(Map<String, Object> body, OAFlowMqConfigEntity oaFlowMqConfigEntity) {
        String ei = oaFlowMqConfigEntity.getTenantId();
        String aplApiName = oaFlowMqConfigEntity.getAplApiName();
        String type = oaFlowMqConfigEntity.getEventType();
        Map<String, Object> dataValueMap = new HashMap<>();
        dataValueMap.put("body", body);
        dataValueMap.put("oaFlowMqConfig", oaFlowMqConfigEntity);

        String obj = null;
        List<FlowEvetTypeEnum> flowEvetTypeEnums = Arrays.stream(FlowEvetTypeEnum.values()).filter(item -> {
            return item.getType().equalsIgnoreCase(type);
        }).collect(Collectors.toList());

        FlowEvetTypeEnum typeEnum = null;
        //这个审批流程处理应该是给搜狐做的
        // https://wiki.firstshare.cn/pages/viewpage.action?pageId=176153511
        if (flowEvetTypeEnums.size() > 0) {
            typeEnum = flowEvetTypeEnums.get(0);
            if ("instance".equalsIgnoreCase(typeEnum.getTarget())) {
                obj = "ApprovalInstanceObj";
            } else if ("bizObject".equalsIgnoreCase(typeEnum.getTarget())) {
                obj = String.valueOf(body.get("tag"));
            } else if ("task".equalsIgnoreCase(typeEnum.getTarget())) {
                obj = "ApprovalTaskObj";
            }
        }
        //查数据详情
        List<ObjectData> dataList = queryData(ei, String.valueOf(body.get("id")), obj);
        dataValueMap.put("dataList", dataList);
        String title = (typeEnum != null ? typeEnum.getName(i18NStringManager,null,ei) : "") + "-";
        //默认取name作为title
        if (dataList.size() > 0) {
            title = title + String.valueOf(dataList.get(0).get("name"));
        } else {
            title = title + oaFlowMqConfigEntity.getEventType();
        }
        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getByTenantId(ei);
        if(CollectionUtils.isNotEmpty(oaConnectInfoEntities)){
            //默认取一个
            // 执行APL代码
            Result<Map<String, Object>> execFuncResult = null;
            OASyncLogSnapshotDoc oaSyncLogEntity =
                    OASyncLogSnapshotDoc.builder().tenantId(ei).dataCenterId(oaConnectInfoEntities.get(0).getId()).dataId(String.valueOf(body.get("id"))).status(OASyncLogEnum.SUCCESS.getSyncType()).id(ObjectId.get()).
                            updateTime(new Date()).createTime(new Date()).objApiName(aplApiName).receiverId(null).
                            eventType(OAEventEnum.DEAL.getEventStatus()).title(title).
                            message("").dataJson(JSONObject.toJSONString(body)).build();
            try {
                execFuncResult = functionUtils.executeFunction(ei, aplApiName, dataValueMap, oaSyncLogEntity);
            } catch (Exception e) {
                log.error(ResultCodeEnum.CUSTOM_FUNC_ERROR + "e:{}" + e.getMessage());
                log.info("OAFlowManager handle exception occurred. execFuncResult:{}", execFuncResult);
                oaSyncLogEntity.setStatus(OASyncLogEnum.EXCEPTION.getSyncType());
                oaSyncLogEntity.setMessage(String.valueOf(e.getMessage()));
            }
            if (!execFuncResult.isSuccess()) {
                oaSyncLogEntity.setMessage(String.valueOf(execFuncResult.getErrMsg()));
                oaSyncLogEntity.setStatus(OASyncLogEnum.FAIL.getSyncType());
                log.info("OAFlowManager handle failed. execFuncResult:{}", execFuncResult);
            }
            Map<String, Object> funcResultData = execFuncResult.getData();

            if (!"0".equals(String.valueOf(funcResultData.get("code")))) {
                oaSyncLogEntity.setStatus(OASyncLogEnum.FAIL.getSyncType());
                oaSyncLogEntity.setMessage(String.valueOf(funcResultData.get("msg")));
            }
            log.info("oaSyncLogEntity:{}", oaSyncLogEntity);
            oaSyncLogSnapshotDao.saveLog(ei,oaSyncLogEntity,null,false);
        }

    }


    public List<ObjectData> queryData(String tenantIdStr, String dataId, String obj) {
        if (obj == null) {
            return Lists.newArrayList();
        }
        Integer tenantId = Integer.valueOf(tenantIdStr);
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantIdStr,i18NStringManager);
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, obj, listArg);
        log.info("queryData data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            log.warn("list queryData failed,tenantId:{},res:{}", tenantId, dataListRes);
            throw new ErpSyncDataException(dataListRes.getMessage(),null,null);
        }
        return dataListRes.getData().getDataList();
    }

    @Cached(cacheType = CacheType.REMOTE, timeUnit = TimeUnit.MINUTES, expire = 100,localLimit = 2000)
    public List<OAFlowMqConfigEntity> queryList(String tenantId,OAFlowMqConfigEntity oaFlowMqConfigEntity) {
            return oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(oaFlowMqConfigEntity);
    }

    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 10,localLimit = 2000)
    public List<String> queryTenantIdList() {
        return oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("-10001")).listTenantId();
    }
}
