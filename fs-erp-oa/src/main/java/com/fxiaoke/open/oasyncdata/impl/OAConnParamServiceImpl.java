package com.fxiaoke.open.oasyncdata.impl;


import com.alibaba.fastjson.JSONObject;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.manager.InitOAApiManager;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2021/1/14
 * @Desc:OA连接参数
 */
@Service("oAConnParamService")
@Slf4j
@Data
public class OAConnParamServiceImpl implements OAConnParamService {
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private InitOAApiManager initOAApiManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private EnterpriseEditionService enterpriseEditionServiceSelf;

    @Override
    public Result<OAConnectInfoVO> getOAConnectInfo(String tenantId,String dataCenterId) {
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId,dataCenterId);
        if (oaConnectInfoEntity == null) {
            return Result.newSuccess();
        }
        OAConnectInfoVO oaConnectInfoVo = new OAConnectInfoVO();
        BeanUtils.copyProperties(oaConnectInfoEntity, oaConnectInfoVo);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        oaConnectInfoVo.setConnectParams(oaConnectParam);
        return Result.newSuccess(oaConnectInfoVo);
    }

    @Override
    public Result<List<OAConnectInfoVO>> listInfoByTenantId(String tenantId) {
        List<OAConnectInfoEntity> byTenantId =
                oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        List<OAConnectInfoVO> oaConnectInfoVOS= Lists.newArrayList();
        try {
            for (OAConnectInfoEntity oaConnectInfoEntity : byTenantId) {
                if(StringUtils.isNotEmpty(oaConnectInfoEntity.getConnectParams())){
                    OAConnectParam oldOAConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
                    OAConnectInfoVO oaConnectInfoVO=new OAConnectInfoVO();
                    BeanUtils.copyProperties(oaConnectInfoEntity,oaConnectInfoVO);
                    oaConnectInfoVO.setConnectParams(oldOAConnectParam);
                    oaConnectInfoVOS.add(oaConnectInfoVO);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.newSuccess(oaConnectInfoVOS);
    }

    @Override
    public Result<String> initOAConnectInfo(String tenantId, String enterpriseName) {
        return initOAApiManager.initApiData(tenantId, enterpriseName);
    }

    @Override
    public Result<String> createOAConnectInfo(String tenantId, String enterpriseName, String connectName, String sourceDataCenterId, boolean copySettings) {
        if(ObjectUtils.isEmpty(enterpriseName)){
            SimpleEnterprise simpleEnterprise = new SimpleEnterprise();
            GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
            getSimpleEnterpriseArg.setEnterpriseId(Integer.valueOf(tenantId));
            GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionServiceSelf.getSimpleEnterprise(getSimpleEnterpriseArg);
            simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
            if (Objects.isNull(simpleEnterprise)||Objects.isNull(simpleEnterprise.getEnterpriseName())){
                return null;
            }
            enterpriseName= simpleEnterprise.getEnterpriseName();
        }
        if(!copySettings){
             String dcId = initOAApiManager.createConnect(tenantId,enterpriseName,connectName);
             return Result.newSuccess(dcId);
        }
        OAConnectInfoEntity oaConnectInfoById = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId, sourceDataCenterId);
        if(ObjectUtils.isEmpty(oaConnectInfoById)){
            return Result.newError(ResultCodeEnum.OA_UN_CONNECT);
        }
        oaConnectInfoById.setId(idGenerator.get());
        oaConnectInfoById.setCreateTime(System.currentTimeMillis());
        oaConnectInfoById.setUpdateTime(System.currentTimeMillis());
        oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(Lists.newArrayList(oaConnectInfoById));
        return Result.newSuccess(oaConnectInfoById.getId());
    }


    @Override
    public Result<String> updateOAConnectInfo(String tenantId, OAConnectInfoVO oaConnectInfoVO) {
        OAConnectInfoEntity oldOAConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId,oaConnectInfoVO.getId());
        OAConnectParam oldOAConnectParam = GsonUtil.fromJson(oldOAConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        OAConnectInfoEntity oaConnectInfoEntity = new OAConnectInfoEntity();
        BeanUtils.copyProperties(oaConnectInfoVO, oaConnectInfoEntity);
        oaConnectInfoVO.getConnectParams().setUrlScript(oldOAConnectParam.getUrlScript());
        oaConnectInfoVO.getConnectParams().setHeaderScript(oldOAConnectParam.getHeaderScript());
        oaConnectInfoVO.getConnectParams().setSsoAuthUrl(oldOAConnectParam.getSsoAuthUrl());
        oaConnectInfoVO.getConnectParams().setCommonMap(oldOAConnectParam.getCommonMap());

        if(oaConnectInfoVO.getConnectParams().getAplApiNames() == null && oldOAConnectParam.getAplApiNames() != null){
            oaConnectInfoVO.getConnectParams().setAplApiNames(oldOAConnectParam.getAplApiNames());
        }

        String oaConnectParam = GsonUtil.toJson(oaConnectInfoVO.getConnectParams());
        oaConnectInfoEntity.setConnectParams(oaConnectParam);
        int i = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaConnectInfoEntity);
        if (i != 0) {
            return Result.newSuccess();
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
    }

    @Override
    public Result<String> coverUpdateOAConnectInfo(String tenantId, OAConnectInfoVO oaConnectInfoVO) {
        OAConnectInfoEntity oaConnectInfoEntity = new OAConnectInfoEntity();
        BeanUtils.copyProperties(oaConnectInfoVO, oaConnectInfoEntity);
        oaConnectInfoEntity.setConnectParams(JSONObject.toJSONString(oaConnectInfoVO.getConnectParams()));
        oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaConnectInfoEntity);
        return Result.newSuccess();

    }
}
