package com.fxiaoke.open.oasyncdata.impl;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.*;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.manager.OAConnectInfoManager;
import com.fxiaoke.open.oasyncdata.model.OAMessageTypeVO;
import com.fxiaoke.open.oasyncdata.model.OASyncApiSettingVo;
import com.fxiaoke.open.oasyncdata.model.OASyncApiVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OASyncApiService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2021/3/19
 * @Desc:OA同步策略查询
 */
@Service
@Slf4j
@Data
public class OASyncApiServiceImpl implements OASyncApiService {
    @Autowired
    private OASyncApiDao oaSyncApiDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private OAConnectInfoManager oaConnectInfoManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    /**
     * 返回具体的配置
     * @param tenantId
     * @return
     */
    @Override
    public Result<List<OASyncApiVO>> getOASyncApiList(String tenantId, List<String> eventTypes,List<String> objectApiNames,String dataCenterId) {
        List<OASyncApiEntity> oaSyncApiEntityList = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getObjApiList(tenantId,dataCenterId,eventTypes,objectApiNames);
        List<OASyncApiVO> oaSyncApiVOList = Lists.newArrayList();
        for (OASyncApiEntity oaSyncApiEntity : oaSyncApiEntityList) {
            OASyncApiVO oaSyncApiVO = new OASyncApiVO();
            BeanUtils.copyProperties(oaSyncApiEntity, oaSyncApiVO);
            oaSyncApiVOList.add(oaSyncApiVO);
        }
        return Result.newSuccess(oaSyncApiVOList);
    }

    /**
     * 只返回对应的状态位
     * @param tenantId
     * @return
     */
    @Override
    public Result<Map<String,Integer>> getOpenApiStatus(String tenantId,String dataCenterId) {
        //以前设计的太耦合，现在要支持多业务类型只能暂且这样支持改动

        List<OASyncApiEntity> oaSyncApiEntityList = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOASyncApiList(tenantId,dataCenterId,null, ApiStatusEnum.OPEN.getStatus());
        Map<String,Integer> crmSettingMap= Maps.newHashMap();
        crmSettingMap.put(CrmTypeMessageEnum.CRM_TODO_TYPE.getType(), 2);
        crmSettingMap.put(CrmTypeMessageEnum.CRM_NOTIFY.getType(),2);
        List<String> toDoEnums = ObjectApiEnum.getStringBusinessEnumByType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType());
        List<String> toNotifyEnums = ObjectApiEnum.getStringBusinessEnumByType(CrmTypeMessageEnum.CRM_NOTIFY.getType());

        for (OASyncApiEntity oaSyncApiEntity : oaSyncApiEntityList) {
            if(toDoEnums.contains(oaSyncApiEntity.getObjApiName())) {
                crmSettingMap.put(CrmTypeMessageEnum.CRM_TODO_TYPE.getType(),1);
            }else if (toNotifyEnums.contains(oaSyncApiEntity.getObjApiName())){
                crmSettingMap.put(CrmTypeMessageEnum.CRM_NOTIFY.getType(),1);
            }
        }

        return Result.newSuccess(crmSettingMap);

    }



    @Override
    public Result<String> updateOASyncApi(List<OASyncApiVO> oaSyncApiVOList, String tenantId,String dataCenterId) {
        for (OASyncApiVO oaSyncApiVO : oaSyncApiVOList) {
            //如果status=3,则需要删除该流程配置的信息
            if(String.valueOf(EventTypeEnum.INVALID.getType()).equals(oaSyncApiVO.getStatus())){
                oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId,oaSyncApiVO.getId());
                continue;
            }
            //如果oaSyncApiVo id没有值，则新增
           if(StringUtils.isEmpty(oaSyncApiVO.getId())){
               OASyncApiEntity oaSyncApiEntity=new OASyncApiEntity();
               BeanUtils.copyProperties(oaSyncApiVO,oaSyncApiEntity);
                oaSyncApiEntity.setId(idGenerator.get());
                oaSyncApiEntity.setDataCenterId(dataCenterId);
                oaSyncApiEntity.setTenantId(tenantId);
                oaSyncApiEntity.setStatus("1");
                oaSyncApiEntity.setUpdateTime(System.currentTimeMillis());
                oaSyncApiEntity.setCreateTime(System.currentTimeMillis());
               oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(Lists.newArrayList(oaSyncApiEntity));
               continue;
            }
            OASyncApiEntity oaSyncApiEntity = new OASyncApiEntity();
            oaSyncApiEntity.setDataCenterId(dataCenterId);
            BeanUtils.copyProperties(oaSyncApiVO, oaSyncApiEntity);
            oaSyncApiEntity.setUpdateTime(System.currentTimeMillis());
            oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaSyncApiEntity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> updateOaApiStatus(OASyncApiSettingVo settingVo, String tenantId,String dataCenterId) {
        Integer count = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateOaApiStatus(tenantId, dataCenterId,settingVo.getEventType(), settingVo.getStatus());
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> deleteOaEvent(String tenantId, List<String> apiIds,String dataCenterId) {
        Integer deleteCount = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchDeleteIds(tenantId, dataCenterId,apiIds);
        return Result.newSuccess(deleteCount);
    }

    @Override
    public Result<Void> updateTodoCustomFunc(String tenantId, String aplApiName,String dataCenterId) {
        oaConnectInfoManager.updateTodoAplApiName(tenantId, aplApiName,dataCenterId);
        return Result.newSuccess();
    }

    @Override
    public Result<List<OAMessageTypeVO>> getBusinessType(String tenantId, String businessType,String dataCenterId,String lang) {
        List<ObjectApiEnum> businessEnumByType = ObjectApiEnum.getBusinessEnumByType(businessType);
        List<OAMessageTypeVO> oaMessageTypeVOS=Lists.newArrayList();
        businessEnumByType.stream().forEach(item ->{
            oaMessageTypeVOS.add(OAMessageTypeVO.builder().type(item.getBusinessType()).objApiName(item.getObjApiName())
                    .description(item.getObjName(i18NStringManager,lang,tenantId)).build());
        });
        return Result.newSuccess(oaMessageTypeVOS);
    }
}
