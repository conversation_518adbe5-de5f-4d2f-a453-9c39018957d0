package com.fxiaoke.open.oasyncdata.manager;

import com.facishare.restful.common.StopWatch;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.EventTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.db.dao.ErpConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.result.Result2;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;

import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 获取crm审批流的数据
 *
 * <AUTHOR>
 * @date 2021/1/4
 */
@Component("approvalTaskManager")
@Slf4j
public class ApprovalTaskManager {
    @Autowired
    private MetadataControllerService metadataControllerService;

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private UserManager userManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;


    /**
     * 查询crm审批数据
     *
     * @param tenantIdStr
     * @param dataId
     * @return
     */
    public List<ObjectData> queryApprovalTaskData(String tenantIdStr, String dataId) {
        Integer tenantId = Integer.valueOf(tenantIdStr);
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantIdStr,i18NStringManager);
        String erpOrgObj = ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName();
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        log.info("ApprovalTaskManager data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            log.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
            throw new ErpSyncDataException(ResultCodeEnum.DATA_NOT_FOUND,tenantIdStr);
        }
        return dataListRes.getData().getDataList();
    }

    /**
     * 返回业务详细数据
     * @param tenantIdStr
     * @param sourceId
     * @param bizApiName
     * @param objDataId
     * @param bizType 业务类型
     * @param eventType 事件类型
     * @return
     */
    public Result<ControllerGetDescribeResult> queryBizDataByType(String tenantIdStr,String dataCenterId, String sourceId,String bizApiName,String objDataId,String bizType,String eventType) {
        StopWatch stopWatch = StopWatch.create("queryCrm");
        String queryDataId="";
        if(bizType.equals(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getBizType())||bizType.equals(ObjectApiEnum.BPM_TASK_OBJ.getBizType())||bizType.equals(ObjectApiEnum.TO_DEAL_STAGE_TASK_OBJ.getBizType())){
            queryDataId=sourceId;
        }else {
            queryDataId=objDataId;
        }
        //新增的不需要查询。
        if(ObjectUtils.isEmpty(queryDataId)&&!EventTypeEnum.ADD.getType().equals(eventType)){
            //非审批对象，业务流程对象的，sourceId是消息id，不代表对象的id.我们需要反查发起业务对象的id查询数据
            OASyncLogMappingDoc oaSyncLogEntities = oaSyncLogMappingsDao.findOneDataByDataId(tenantIdStr, dataCenterId,bizApiName, sourceId);
            if(ObjectUtils.isNotEmpty(oaSyncLogEntities)){
                bizApiName = oaSyncLogEntities.getObjApiName();
                queryDataId=oaSyncLogEntities.getBusinessDataId();
            }
        }
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantIdStr,i18NStringManager);
        headerObj.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantIdStr));
        ControllerDetailArg controllerDetailArg=new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(queryDataId);
        controllerDetailArg.setObjectDescribeApiName(bizApiName);
        controllerDetailArg.setIsFromRecycleBin(Boolean.TRUE);
        Result<ControllerGetDescribeResult> detail = metadataControllerService.detail(headerObj, bizApiName, controllerDetailArg);
        stopWatch.lap("metaDetail");
        stopWatch.log();
        return detail;

    }

    /**
     * 根据biztype查询对应的对象数据
     * @param
     * @return
     */
    public Result2<ControllerGetDescribeResult> queryAllStatusBizDataByType(String tenantIdStr,String dataCenterId,String sourceId,String objDataId,String bizType, String bizApiName) {

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantIdStr,i18NStringManager);
        String queryDataId="";
        if(bizType.equals(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getBizType())||bizType.equals(ObjectApiEnum.BPM_TASK_OBJ.getBizType())||bizType.equals(ObjectApiEnum.TO_DEAL_STAGE_TASK_OBJ.getBizType())){
            queryDataId=sourceId;
        }else {
            queryDataId=objDataId;
        }
        //新增的不需要查询。
        if(ObjectUtils.isEmpty(queryDataId)){
            //非审批对象，业务流程对象的，sourceId是消息id，不代表对象的id.我们需要反查发起业务对象的id查询数据
            OASyncLogMappingDoc oaSyncLogEntities = oaSyncLogMappingsDao.findOneDataByDataId(tenantIdStr, dataCenterId,bizApiName, sourceId);
            if(ObjectUtils.isNotEmpty(oaSyncLogEntities)){
                bizApiName = oaSyncLogEntities.getObjApiName();
                queryDataId=oaSyncLogEntities.getBusinessDataId();
            }
        }

        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> controllerGetDescribeResult = objectDescribeService.getDescribe(headerObj, bizApiName);
        com.fxiaoke.open.oasyncdata.result.base.Result<ObjectData> allStatusResult = getAllStatusData(bizApiName, tenantIdStr, queryDataId);
        if(allStatusResult.isSuccess()){
            controllerGetDescribeResult.getData().setData(allStatusResult.getData());
            return Result2.newSuccess(controllerGetDescribeResult.getData());
        }
        return Result2.newError(com.fxiaoke.open.oasyncdata.result.ResultCodeEnum.PARAM_ERROR);

    }

    public com.fxiaoke.open.oasyncdata.result.base.Result<ObjectData> getAllStatusData(String objectApiName, String tenantId, String id){
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        headerObj.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantId));
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.setSearchSource("db");
        searchQueryInfo.addFilter("_id",Collections.singletonList(id), FilterOperatorEnum.EQ);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2","-1","0","1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> resultResult = objectDataServiceV3.findOne(headerObj, findV3Arg);
        ObjectData objectData = new ObjectData();
        try {
            ObjectDataGetByIdV3Result data = resultResult.getData();
            if (data == null || data.getObjectData() == null) {
                log.warn("not found data,obj:{},id:{}", objectApiName, id);
                return com.fxiaoke.open.oasyncdata.result.base.Result.newSystemError(I18NStringEnum.s985);
            }
            objectData.putAll(data.getObjectData());
        } catch (CrmBusinessException e) {
            log.warn("getObjectData CrmBusinessException = "+ e.toString());
            return com.fxiaoke.open.oasyncdata.result.base.Result.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        }
        return com.fxiaoke.open.oasyncdata.result.base.Result.newSuccess(objectData);
    }



}
