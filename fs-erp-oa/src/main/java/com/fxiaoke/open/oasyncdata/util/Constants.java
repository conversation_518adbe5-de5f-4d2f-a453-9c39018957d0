package com.fxiaoke.open.oasyncdata.util;

public class Constants {

    public static final String SESSION_CURRENT_OPEN_USER_ID = "currentOpenUserId";

    private Constants() {}

    /**
     * 接口返回码
     * 
     * <AUTHOR>
     *
     */
    public enum interfaceResponseCode {

        ACCESS_TOKEN_EXPIRED(28069, "The accesstoken has expired");

        public int code;

        public String msg;

        private interfaceResponseCode(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

//    /**
//     * 异常码定义
//     *
//     * <AUTHOR>
//     *
//     */
//    public enum interfaceException {
//
//        INTERFACE_EXCEPTION(400, "调用接口失败");
//
//        public int code;
//
//        public String msg;
//
//        private interfaceException(int code, String msg) {
//            this.code = code;
//            this.msg = msg;
//        }
//    }

}
