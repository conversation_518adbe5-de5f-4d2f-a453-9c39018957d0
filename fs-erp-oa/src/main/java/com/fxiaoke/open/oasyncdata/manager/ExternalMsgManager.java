package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.message.extrnal.platform.model.arg.BaseExternalArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.*;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OARequestModel;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/11 16:39 crm提醒的发布
 * @Version 1.0
 */
@Component
@Slf4j
public class ExternalMsgManager {
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;
    @Autowired
    private ApprovalTaskManager approvalTaskManager;
    @Autowired
    private OARequestManager oaRequestManager;
    @Autowired
    private OACommonFieldManager oaCommonFieldManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private OASyncApiDao oaSyncApiDao;

    public Result<String> sendTextMessage(SendTextMessageArg arg,String dataCenterId) {
        log.info("send text json:{},",JSONObject.toJSONString(arg));
        String tenantId = String.valueOf(arg.getEi());
        Result<String> modelResult = convertModel(arg, tenantId, arg.getReceiverIds(),OAMessageTag.TEXT_MSG_TAG,dataCenterId);
        return modelResult;
    }

    public Result<String> sendTextCardMessage(SendTextCardMessageArg arg,String dataCenterId) {
        log.info("send sendTextCardMessage json:{},",JSONObject.toJSONString(arg));
        String tenantId = String.valueOf(arg.getEi());
        Result<String> modelResult = convertModel(arg, tenantId, arg.getReceiverIds(),OAMessageTag.CARD_MSG_TAG,dataCenterId);
        return modelResult;
    }

    private Result<String> convertModel(BaseExternalArg baseExternalArg, String tenantId, List<Integer> userIds, String messageType,String dataCenterId) {
        String jsonArg = new Gson().toJson(baseExternalArg);
        //判断是否为空
        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId,oaConnectInfoEntity.getId(), OAEventEnum.CREATE.getEventStatus(),
                    ObjectApiEnum.CRM_NOTIFY.getObjApiName());
            if (oaSyncApiEntity == null || oaConnectInfoEntity == null) {
                continue;
            }
             dataCenterId=oaConnectInfoEntity.getId();
            log.info("external arg:{}", JSONObject.toJSONString(baseExternalArg));
            OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
            //转换参数
            Map<String, Object> dataMap = generateParamsMap(baseExternalArg,tenantId,dataCenterId);
            List<String> userLists = userIds.stream().map(item -> String.valueOf(item)).collect(Collectors.toList());
            List<OARequestModel> oaRequestModels = oaCommonFieldManager.generateExpressionRequest(tenantId,oaConnectInfoEntity.getId(), dataMap, oaSyncApiEntity, userLists);
            OASyncLogSnapshotDoc oaSyncLogSnapshotDoc = new OASyncLogSnapshotDoc();
            for (OARequestModel oaRequestModel : oaRequestModels) {
                try {
                    Result<String> result = oaRequestManager.callCustomFuncAndRestfulService(tenantId,
                            oaConnectParam,
                            oaRequestModel,
                            oaSyncApiEntity.getUrl(),
                            oaSyncApiEntity.getRequestMode(),
                            dataCenterId,
                            oaSyncLogSnapshotDoc);
                    String status = result.isSuccess() ? OASyncLogEnum.SUCCESS.getSyncType() : OASyncLogEnum.FAIL.getSyncType();
                    OASyncLogSnapshotDoc OASyncLogEntity =
                            OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dataMap.get("objectId").toString()).dataCenterId(oaConnectInfoEntity.getId()).status(status).businessType(CrmTypeMessageEnum.CRM_NOTIFY.getType()).id(ObjectId.get()).
                                    updateTime(new Date()).createTime(new Date()).objApiName(ObjectApiEnum.CRM_NOTIFY.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                    eventType(OAEventEnum.CREATE.getEventStatus()).messageType(messageType).title(i18NStringManager.getByEi(I18NStringEnum.s1242,tenantId)).objectName("")
                                    .dataName("").message(result.getErrMsg()).dataJson(jsonArg).build();
                    OASyncLogEntity.setUrl(oaSyncLogSnapshotDoc.getUrl());
                    OASyncLogEntity.setAplApiName(oaSyncLogSnapshotDoc.getAplApiName());
                    OASyncLogEntity.setMethod(oaSyncLogSnapshotDoc.getMethod());
                    OASyncLogEntity.setHeader(oaSyncLogSnapshotDoc.getHeader());
                    OASyncLogEntity.setBody(oaSyncLogSnapshotDoc.getBody());
                    OASyncLogEntity.setResponse(oaSyncLogSnapshotDoc.getResponse());
                    log.info("external oa {},result:{},insert", oaRequestModel, OASyncLogEntity);
                    oaSyncLogSnapshotDao.saveLog(tenantId, OASyncLogEntity, ObjectId.get(),false);
                } catch (Exception e) {
                    log.info("sendTextMessage exception:{}", e.getMessage());
                    OASyncLogSnapshotDoc OASyncLogEntity =
                            OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dataMap.get("objectId").toString()).dataCenterId(oaConnectInfoEntity.getId()).businessType(CrmTypeMessageEnum.CRM_NOTIFY.getType()).status(OASyncLogEnum.FAIL.getSyncType()).id(ObjectId.get()).
                                    updateTime(new Date()).createTime(new Date()).objApiName(ObjectApiEnum.CRM_NOTIFY.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                    eventType(OAEventEnum.CREATE.getEventStatus()).messageType(messageType).title(i18NStringManager.getByEi(I18NStringEnum.s1242,tenantId)).objectName("")
                                    .dataName(dataMap.get("data_name").toString()).message(e.getMessage()).dataJson(jsonArg).build();
                    OASyncLogEntity.setUrl(oaSyncLogSnapshotDoc.getUrl());
                    OASyncLogEntity.setAplApiName(oaSyncLogSnapshotDoc.getAplApiName());
                    OASyncLogEntity.setMethod(oaSyncLogSnapshotDoc.getMethod());
                    OASyncLogEntity.setHeader(oaSyncLogSnapshotDoc.getHeader());
                    OASyncLogEntity.setBody(oaSyncLogSnapshotDoc.getBody());
                    OASyncLogEntity.setResponse(oaSyncLogSnapshotDoc.getResponse());
                    log.info("external oa {},result:{},insert", oaRequestModel, OASyncLogEntity);
                    oaSyncLogSnapshotDao.saveLog(tenantId, OASyncLogEntity, ObjectId.get(),false);
                }
            }
        }


        return Result.newSuccess();
    }

    private Map<String, Object> generateParamsMap(BaseExternalArg baseExternalArg,String tenantId,String dataCenterId) {
        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("objectId", convertGenerateCustomId(baseExternalArg));
        paramsMap.put("data_name", "");
        paramsMap.put("objectApiName", "");
        paramsMap.put("workflowInstanceId", "");
        if (baseExternalArg instanceof SendTextCardMessageArg) {
            String enterpriseAccount = ((SendTextCardMessageArg) baseExternalArg).getEa();
            String tenantId2 = String.valueOf(((SendTextCardMessageArg) baseExternalArg).getEi());
            //消息卡片的参数转成
            paramsMap.put("title", ((SendTextCardMessageArg) baseExternalArg).getTitle());
            paramsMap.put("messageContent", ((SendTextCardMessageArg) baseExternalArg).getMessageContent());
            paramsMap.put("enterpriseAccount", ((SendTextCardMessageArg) baseExternalArg).getEa());
            paramsMap.put("enterpriseId", ((SendTextCardMessageArg) baseExternalArg).getEi());
            paramsMap.put("type", "card");
            paramsMap.put("appId", ((SendTextCardMessageArg) baseExternalArg).getReceiverChannelData());
            if (baseExternalArg.getExtraDataMap().size() != 0) {
                String apiName = baseExternalArg.getExtraDataMap().get("objectApiName");
                String objectId = Optional.ofNullable(baseExternalArg.getExtraDataMap().get("objectId")).orElseGet(() -> convertGenerateCustomId(baseExternalArg));
                paramsMap.put("objectApiName", apiName);
                paramsMap.put("workflowInstanceId", baseExternalArg.getExtraDataMap().get("workflowInstanceId"));
                paramsMap.put("objectId", objectId);
                if (StringUtils.isNotEmpty(baseExternalArg.getExtraDataMap().get("objectApiName")) &&
                        StringUtils.isNotEmpty(baseExternalArg.getExtraDataMap().get("objectId"))
                ) {
                    //查询业务对象的返回
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = approvalTaskManager.queryBizDataByType(tenantId2,dataCenterId, objectId, apiName,objectId,ObjectApiEnum.CRM_NOTIFY.getBizType(),null);
                    ObjectData objectData = describeResult.getData().getData();
                    paramsMap.putAll(objectData);
                }
            }
        }
        if (baseExternalArg instanceof SendTextMessageArg) {
            //文本消息的参数
            paramsMap.put("title", i18NStringManager.getByEi(I18NStringEnum.s1242,tenantId));
            paramsMap.put("messageContent", ((SendTextMessageArg) baseExternalArg).getMessageContent());
            paramsMap.put("enterpriseAccount", ((SendTextMessageArg) baseExternalArg).getEa());
            paramsMap.put("enterpriseId", ((SendTextMessageArg) baseExternalArg).getEi());
            paramsMap.put("type", "text");
            paramsMap.put("appId",((SendTextMessageArg) baseExternalArg).getReceiverChannelData());
        }
        paramsMap.put("prototype",JSONObject.toJSONString(baseExternalArg));
        return paramsMap;
    }

    private String convertGenerateCustomId(BaseExternalArg baseExternalArg) {
        return idGenerator.get();
//        try {
//            String multiContent = ((SendTextMessageArg) baseExternalArg).getMessageContent() + ((SendTextMessageArg) baseExternalArg).getEa() + ((SendTextMessageArg) baseExternalArg).getReceiverIds();
//            return String.valueOf(HashUtil.tianlHash(multiContent));
//        } catch (Exception e) {
//            return idGenerator.get();
//        }
    }

//    public static void main(String[] args) {
//        String content = "业务对象id(卡片消息类型时不为空)2222业务对象id(卡片消息类型时不为空)业务对象id(卡片消息类型时不为空)业务对象id(卡片消息类型时不为空)";
//        long data = HashUtil.tianlHash(content);
//        System.out.printf(data + "");
//    }


}
