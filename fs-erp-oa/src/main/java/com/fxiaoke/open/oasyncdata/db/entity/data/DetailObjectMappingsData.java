package com.fxiaoke.open.oasyncdata.db.entity.data;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public class DetailObjectMappingsData extends ArrayList<DetailObjectMappingsData.DetailObjectMappingData> {
    @Data
    public static class DetailObjectMappingData {
        /** 源从对象apiName */
        private String sourceObjectApiName;
        /** 目标从对象apiName */
        private String destObjectApiName;
        private List<FieldMappingData> fieldMappings = new ArrayList<>();
    }
}
