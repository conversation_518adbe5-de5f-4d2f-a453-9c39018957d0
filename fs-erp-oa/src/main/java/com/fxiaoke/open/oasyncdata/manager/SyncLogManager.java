package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.auditlog.api.bean.BizType;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.*;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncLogEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OARequestModel;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.util.FunctionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * 处理日志信息业务（包含重试）
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Component("syncLogManager")
@Slf4j
public class SyncLogManager {
    @Autowired
    private OASyncLogDao oaSyncLogDao;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;
    @Autowired
    private OARequestManager oaRequestManager;

    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private IdGenerator idGenerator;


    @Autowired
    private OASyncApiDao oaSyncApiDao;

    @Autowired
    private ApprovalTaskManager approvalTaskManager;

    @Autowired
    private ExternalToDoManager externalToDoManager;

    @Autowired
    private FunctionUtils customFuncManager;
    @Autowired
    private OACommonFieldManager oACommonFieldManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private I18NStringManager i18NStringManager;


    /**
     * 重试单条数据
     *
     * @param tenantId
     * @param logId
     * @return
     */
    public Result<String> reSyncData(String tenantId, String dataCenterId, String mappingLogId, String lang) {
        log.info("reSyncData start id={}", mappingLogId);
        OASyncLogMappingDoc getLogMappingsData = oaSyncLogMappingsDao.getById(tenantId, dataCenterId, new ObjectId(mappingLogId));
        if (ObjectUtils.isEmpty(getLogMappingsData)) {
            log.info("notfound message:{}",mappingLogId);
            return Result.newError(ResultCodeEnum.DATA_NOT_FOUND);
        }
        if (ObjectUtils.isEmpty(getLogMappingsData.getDataCenterId())) {
            List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
            dataCenterId = oaConnectInfoEntities.get(0).getId();
        }
        OASyncLogSnapshotDoc oaSyncLogEntity = oaSyncLogSnapshotDao.getById(tenantId, getLogMappingsData.getLastSyncLogId().toString());
//        OASyncLogEntity oaSyncLogEntity = oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(logId);
        if (oaSyncLogEntity == null) {
            return Result.newError(ResultCodeEnum.RESULT_NULL);
        }
        ObjectId lastLogId = ObjectId.get();
        oaSyncLogEntity.setId(lastLogId);
        oaSyncLogEntity.setCreateTime(new Date());
        oaSyncLogEntity.setUpdateTime(new Date());
        Result<String> result = Result.newSuccess();
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(oaSyncLogEntity.getTenantId())).getOAConnectInfoById(oaSyncLogEntity.getTenantId(), dataCenterId);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        if (oaSyncLogEntity.getStatus().equals(OASyncLogEnum.FAIL.getSyncType())) {
            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(oaSyncLogEntity.getTenantId(), oaConnectInfoEntity.getId(), oaSyncLogEntity.getEventType(),
                    oaSyncLogEntity.getObjApiName());
            if (StringUtils.isEmpty(oaSyncLogEntity.getReceiverId()) && OAEventEnum.DELETE.getEventStatus().equals(oaSyncLogEntity.getEventType())) {
                result = reByCallJson(oaSyncApiEntity, oaConnectParam, oaSyncLogEntity,mappingLogId);
            } else {
                result = reByDataId(oaSyncApiEntity, oaConnectInfoEntity.getId(), oaSyncLogEntity, oaConnectParam, lang,mappingLogId);
            }

        } else if (oaSyncLogEntity.getStatus().equals(OASyncLogEnum.EXCEPTION.getSyncType()) || oaSyncLogEntity.getStatus().equals(OASyncLogEnum.SYNC.getSyncType())) {
            reByOriginalRequest(oaSyncLogEntity.getEventType(), oaSyncLogEntity.getDataJson(),dataCenterId);
            log.info("reSyncData end id={}", mappingLogId);
            return Result.newSuccess();
        } else {
            log.info("reSyncData end id={}", mappingLogId);
            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(oaSyncLogEntity.getTenantId(), oaConnectInfoEntity.getId(), oaSyncLogEntity.getEventType(),
                    oaSyncLogEntity.getObjApiName());
            result = reByCallJson(oaSyncApiEntity, oaConnectParam, oaSyncLogEntity,mappingLogId);
        }
        log.info("reSyncData end id={}", lastLogId);
        OASyncLogMappingDoc copyMappingDoc = BeanUtil.copy(oaSyncLogEntity, OASyncLogMappingDoc.class);
        copyMappingDoc.setLastSyncLogId(lastLogId);
        oaSyncLogMappingsDao.updateByObjectId(tenantId, getLogMappingsData.getId(), copyMappingDoc);

        return result;
    }

    private void reByOriginalRequest(String eventType, String requestJson,String dataCenterId) {
        if (eventType.equals(OAEventEnum.CREATE.getEventStatus())) {
            CreateTodoArg createTodoArg = GsonUtil.fromJson(requestJson, CreateTodoArg.class);
            externalToDoManager.createTodo(createTodoArg,true,dataCenterId);
        } else if (eventType.equals(OAEventEnum.DEAL.getEventStatus())) {
            DealTodoArg dealTodoArg = GsonUtil.fromJson(requestJson, DealTodoArg.class);
            externalToDoManager.dealTodo(dealTodoArg,true,dataCenterId);
        } else {
            DeleteTodoArg deleteTodoArg = GsonUtil.fromJson(requestJson, DeleteTodoArg.class);
            externalToDoManager.deleteTodo(deleteTodoArg,true,dataCenterId);
        }
    }

    private Result<String> reByDataId(OASyncApiEntity oaSyncApiEntity, String dataCenterId, OASyncLogSnapshotDoc oaSyncLogEntity, OAConnectParam oaConnectParam, String lang,String mappingLogId) {
        if (ObjectUtils.isEmpty(oaSyncApiEntity)) {
            return Result.newError(ResultCodeEnum.OA_SETTING_NOT_OPEN);
        }
        String tenantId = oaSyncApiEntity.getTenantId();
        String dataId = oaSyncLogEntity.getDataId();
        String userId = oaSyncLogEntity.getReceiverId();
        String eventType = oaSyncLogEntity.getEventType();
        List<OARequestModel> oaRequestModelList = Lists.newArrayList();
        if (ObjectApiEnum.assistTodoType(oaSyncApiEntity.getObjApiName())) {
            Result<List<OARequestModel>> listResult = convertTodoModel(tenantId, dataId, oaSyncLogEntity, oaSyncApiEntity, userId, eventType, dataCenterId);
            if(!listResult.isSuccess()){
                log.info("data is delete by crm :{}", dataId);
                return Result.newError(listResult.getErrCode(),listResult.getErrMsg(),listResult.getI18nKey());
            }
            oaRequestModelList=listResult.getData();
        } else {
            OARequestModel oaRequestModel = new OARequestModel();
            oaRequestModel.setReceiverId(userId);
            oaRequestModel.setRequestJson(oaSyncLogEntity.getDataJson());
            String oaUserCode = userManager.getOAUserCode(userId, tenantId, dataCenterId);
            oaRequestModel.setOaReceiverId(oaUserCode);
            oaRequestModelList.add(oaRequestModel);
        }
        Result<String> result = Result.newError(ResultCodeEnum.OA_USER_NOT_BIND);
        for (OARequestModel oaRequestModel : oaRequestModelList) {
            result = oaRequestManager.callCustomFuncAndRestfulService(tenantId,
                    oaConnectParam,
                    oaRequestModel,
                    oaSyncApiEntity.getUrl(),
                    oaSyncApiEntity.getRequestMode(),
                    dataCenterId,
                    oaSyncLogEntity);
            oaSyncLogEntity.setReceiverId(oaRequestModel.getReceiverId());
            if (!result.isSuccess()) {
                oaSyncLogEntity.setStatus(OASyncLogEnum.FAIL.getSyncType());
            } else {
                oaSyncLogEntity.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
            }
            oaSyncLogEntity.setDataJson(oaRequestModel.getRequestJson());
            oaSyncLogEntity.setMessage(i18NStringManager.get2(result.getI18nKey(), lang, tenantId, result.getErrMsg(), result.getI18nExtra()));
            log.info("save log message mapping:{}:{}",mappingLogId,oaSyncLogEntity.getDataId());
            oaSyncLogSnapshotDao.saveLog(oaSyncLogEntity.getTenantId(), oaSyncLogEntity, new ObjectId(mappingLogId),true);

        }
        return result;
    }

    private Result<List<OARequestModel>> convertTodoModel(String tenantId, String dataId, OASyncLogSnapshotDoc oaSyncLogEntity, OASyncApiEntity oaSyncApiEntity, String userId, String eventType, String dataCenterId) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = approvalTaskManager.queryBizDataByType(tenantId, dataCenterId, dataId, oaSyncLogEntity.getObjApiName(), oaSyncLogEntity.getDataId(), oaSyncLogEntity.getObjApiName(), eventType);
        if(!describeResult.isSuccess()){
            return Result.newError(ResultCodeEnum.OA_TODO_IS_DELETE);
        }
        // 判断审批的对象
        if(oaSyncApiEntity.getObjApiName().equals(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName())){
            if(oaSyncApiEntity.getEventType().equals(EventTypeEnum.ADD.getType())){
                //新增判断是不是数据已经是已处理，这种就需要忽略
                ObjectData data = describeResult.getData().getData();
                Object state = data.get("state");
                if(ObjectUtils.isNotEmpty(state)&&!"in_progress".equals(state.toString())){
                  //重试的数据，只支持待办还没处理，避免有重复的数据推送
                    log.info("data state is not need process:{}:{}", dataId,state);
                    return Result.newError(ResultCodeEnum.OA_TO_IS_PAAS);
                }
            }
        }

        ObjectData objectData = describeResult.getData().getData();
        log.info("reByDataId objectData:{}", JSONObject.toJSONString(objectData));

        List<Integer> userIdList = Lists.newArrayList();
        // userId为空就全量同步该审批（删除事件除外）
        if (StringUtils.isNotEmpty(userId)) {
            userIdList.add(Integer.valueOf(userId));
        } else {
            userIdList = null;
        }
        List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeOAField(oaSyncApiEntity.getTenantId(), dataCenterId, oaSyncApiEntity, describeResult.getData(),
                userIdList);
        return Result.newSuccess(oaRequestModelList);
    }


    private Result<String> reByCallJson(OASyncApiEntity oaSyncApiEntity, OAConnectParam oaConnectParam, OASyncLogSnapshotDoc oaSyncLogEntity,String mappingLogId) {
        if (ObjectUtils.isEmpty(oaSyncApiEntity)) {
            return Result.newError(ResultCodeEnum.OA_SETTING_NOT_OPEN);
        }
        String todoAplApiName = oaConnectParam.getAplApiName(OAAPLTypeEnum.TODO);
        Result<String> requestDataResult = null;
        if (StringUtils.isNotEmpty(todoAplApiName)) {
            requestDataResult = customFuncManager.getRequestData(oaSyncApiEntity.getTenantId(),
                    todoAplApiName,
                    oaSyncLogEntity.getDataJson(),
                    oaSyncLogEntity);
            if(requestDataResult.isSuccess()) {
                oaSyncLogEntity.setDataJson(requestDataResult.getData());
            }
        }
        Result<String> result = null;
        if (requestDataResult != null && requestDataResult.isSuccess() == false) {
            result = requestDataResult;
        } else {
            result = oaRequestManager.callRestfulService(oaConnectParam,
                    oaSyncApiEntity.getUrl(),
                    oaSyncLogEntity.getDataJson(),
                    oaSyncLogEntity.getTenantId(),
                    oaSyncApiEntity.getRequestMode(),
                    oaSyncLogEntity);
        }
        if (!result.isSuccess()) {
            oaSyncLogEntity.setStatus(OASyncLogEnum.FAIL.getSyncType());
        } else {
            oaSyncLogEntity.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
        }
        oaSyncLogEntity.setMessage(result.getErrMsg());
        oaSyncLogSnapshotDao.saveLog(oaSyncLogEntity.getTenantId(), oaSyncLogEntity, new ObjectId(mappingLogId),true);
        return result;
    }



}
