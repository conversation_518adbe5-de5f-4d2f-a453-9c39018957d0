package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.open.oasyncdata.annotation.LogLevel;
import com.fxiaoke.open.oasyncdata.db.constant.CommonConstant;
import com.fxiaoke.open.oasyncdata.model.ReadExcel;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/20
 **/
@Service
@Slf4j
public class FileManager {
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private StoneProxyApi stoneProxyApi;

    @LogLevel
    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile get result null");
        }
        String ei = eieaConverter.enterpriseAccountToId(ea)+"";
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,ei);
    }

    public <E> void readExcel(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        final ExcelReaderBuilder builder = EasyExcel.read(inputStream, arg.getType(), listener);
        if (Objects.nonNull(arg.getExcelType())) {
            builder.excelType(arg.getExcelType());
        }
        builder.sheet().doRead();
    }

    public <E> void readExcelBySheetName(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).sheet(arg.getSheetName()).doRead();
    }

    @SneakyThrows
    public InputStream downloadByPath(String tenantId, String path, String suffix) {
        final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        StoneFileDownloadRequest downloadRequest = new StoneFileDownloadRequest();
        downloadRequest.setFileType(suffix);
        downloadRequest.setCancelRemoteThumb(false);
        downloadRequest.setPath(path);
        downloadRequest.setSecurityGroup("");
        downloadRequest.setEa(ea);
        downloadRequest.setEmployeeId(1000);
        downloadRequest.setBusiness("FMCG-SALES");
        return stoneProxyApi.downloadStream(downloadRequest);
    }
}
