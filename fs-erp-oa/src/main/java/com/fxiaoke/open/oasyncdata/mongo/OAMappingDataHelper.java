package com.fxiaoke.open.oasyncdata.mongo;

import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.mongodb.client.model.Updates;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/9/8 20:03
 * @Version 1.0
 */
@UtilityClass
public class OAMappingDataHelper  {

    public Bson updateByEiObjDataNum(OASyncLogMappingDoc message) {
        Document result = new Document();
        result.put("tenantId", message.getTenantId());
        result.put("dataCenterId", message.getDataCenterId());
        result.put("eventType", message.getEventType());
        result.put("dataId", message.getDataId());
        result.put("receiverId",message.getReceiverId());
        return result;
    }

    public Bson updateResyncMessage(OAResyncLogDoc oaResyncLogDoc) {
        Document result = new Document();
        result.put("tenantId", oaResyncLogDoc.getTenantId());
        result.put("dataCenterId", oaResyncLogDoc.getDataCenterId());
        result.put("eventType", oaResyncLogDoc.getEventType());
        result.put("dataId", oaResyncLogDoc.getDataId());
        result.put("receiverId",oaResyncLogDoc.getReceiverId());
        return result;
    }
    public Bson updateIncrMessage() {
        Document result = new Document();
        result.put("$inc",new Document().put("retryCount",1));
        return result;
    }

    public Bson upsert(OASyncLogMappingDoc message){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        if(message.getUpdateTime()!=null){
            updateDoc.append("updateTime", message.getUpdateTime());
        }else{
            updateDoc.append("updateTime", new Date());
        }

        //不为空,才更新
        if(message.getLastSyncLogId()!=null){
            updateDoc.append("lastSyncLogId", message.getLastSyncLogId());
        }
        if (StringUtils.isNotBlank(message.getStatus())) {
            updateDoc.append("status", message.getStatus());
        }
        if ( StringUtils.isNotBlank(message.getDataName())) {
            updateDoc.append("dataName", message.getDataName());
        }
        if (StringUtils.isNotBlank(message.getObjectName())) {
            updateDoc.append("objectName", message.getObjectName());
        }
        if (StringUtils.isNotBlank(message.getMessage())) {
            updateDoc.append("message", message.getMessage());
        }
        if (StringUtils.isNotBlank(message.getObjApiName())) {
            updateDoc.append("objApiName", message.getObjApiName());
        }
        if (StringUtils.isNotBlank(message.getTitle())) {
            updateDoc.append("title", message.getTitle());
        }
        if (StringUtils.isNotBlank(message.getBusinessDataId())) {
            updateDoc.append("businessDataId", message.getBusinessDataId());
        }
        if (StringUtils.isNotBlank(message.getBusinessType())) {
            updateDoc.append("businessType", message.getBusinessType());
        }
        if (StringUtils.isNotBlank(message.getMessageType())) {
            updateDoc.append("messageType", message.getMessageType());
        }
        //插入
        setOnInsertDoc
                .append("tenantId", message.getTenantId())
                .append("dataCenterId", message.getDataCenterId())
                .append("dataId", message.getDataId())
                .append("eventType", message.getEventType())
                .append("receiverId", message.getReceiverId())
                .append("_id",message.getId())
                .append("createTime",message.getCreateTime());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }


    public Bson updateByType(OASettingDoc message) {
        Document result = new Document();
        result.put("tenantId", message.getTenantId());
        result.put("dataCenterId", message.getDataCenterId());
        result.put("type", message.getType());
        return result;
    }
    public Bson updateByTypeFilter(OASettingDoc message) {
        Document result = new Document();
        result.put("tenantId", message.getTenantId());
        result.put("type", message.getType());
        return result;
    }

    public Bson upsertOaSettings(OASettingDoc message){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        if(message.getUpdateTime()!=null){
            updateDoc.append("updateTime", message.getUpdateTime());
        }else{
            updateDoc.append("updateTime", new Date());
        }


        if (StringUtils.isNotBlank(message.getConfiguration())) {
            updateDoc.append("configuration", message.getConfiguration());
        }
        if (StringUtils.isNotBlank(message.getDataCenterId())) {
            updateDoc.append("dataCenterId", message.getDataCenterId());
        }

        //插入
        setOnInsertDoc
                .append("tenantId", message.getTenantId())
                .append("type", message.getType())
                .append("_id",message.getId()).append("createTime",new Date());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }

    public Bson upsertMessageResync(OAResyncLogDoc message){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        if(message.getUpdateTime()!=null){
            updateDoc.append("updateTime", message.getUpdateTime());
        }else{
            updateDoc.append("updateTime", new Date());
        }

        if (StringUtils.isNotBlank(message.getStatus())) {
            updateDoc.append("status", message.getStatus());
        }
        if ( StringUtils.isNotBlank(message.getDataName())) {
            updateDoc.append("dataName", message.getDataName());
        }
        if (StringUtils.isNotBlank(message.getObjectName())) {
            updateDoc.append("objectName", message.getObjectName());
        }
        if (StringUtils.isNotBlank(message.getMessage())) {
            updateDoc.append("message", message.getMessage());
        }
        if (StringUtils.isNotBlank(message.getObjApiName())) {
            updateDoc.append("objApiName", message.getObjApiName());
        }
        if (StringUtils.isNotBlank(message.getTitle())) {
            updateDoc.append("title", message.getTitle());
        }
        if (StringUtils.isNotBlank(message.getBusinessDataId())) {
            updateDoc.append("businessDataId", message.getBusinessDataId());
        }
        if (StringUtils.isNotBlank(message.getBusinessType())) {
            updateDoc.append("businessType", message.getBusinessType());
        }
        if (ObjectUtils.isNotEmpty(message.getNextRetryTime())) {
            updateDoc.append("nextRetryTime", message.getNextRetryTime());
        }
        if (ObjectUtils.isNotEmpty(message.getNextRetryTime())) {
            updateDoc.append("nextRetryTime", message.getNextRetryTime());
        }
        if (ObjectUtils.isNotEmpty(message.getMessageType())) {
            updateDoc.append("messageType", message.getMessageType());
        }
        if (ObjectUtils.isNotEmpty(message.getRetryCount())) {
            updateDoc.append("retryCount", message.getRetryCount());
        }
        if (ObjectUtils.isNotEmpty(message.getDataJson())) {
            updateDoc.append("dataJson", message.getDataJson());
        }
        if (ObjectUtils.isNotEmpty(message.getDataMappingId())) {
            updateDoc.append("dataMappingId", message.getDataMappingId());
        }

        //插入
        setOnInsertDoc
                .append("tenantId", message.getTenantId())
                .append("dataCenterId", message.getDataCenterId())
                .append("dataId", message.getDataId())
                .append("eventType", message.getEventType())
                .append("receiverId", message.getReceiverId())
                .append("retryCount",0)
                .append("_id",message.getId()).append("createTime",message.getCreateTime());

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        return doc;
    }

}
