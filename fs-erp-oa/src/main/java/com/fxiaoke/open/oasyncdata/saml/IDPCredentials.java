package com.fxiaoke.open.oasyncdata.saml;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.opensaml.security.credential.Credential;

import java.io.IOException;
import java.io.InputStream;

/**
 * 本DEMO 作为IDP 接入时所使用的用来保存 凭据 的类
 */
@Slf4j
public class IDPCredentials {

    private static Credential credential;

    private static Credential resolveCredential(String password) {
        try {
            String privateKey = IOUtils.toString(IDPCredentials.class.getResource("/cert/rsa_aes_private.key"),"UTF-8");
            String certificate = IOUtils.toString(IDPCredentials.class.getResource("/cert/client.pem"),"UTF-8");
            return SamlKeyStoreProvider.getCredential(privateKey, certificate,password);
        }catch (IOException e){
            throw new RuntimeException(e);
        }
    }

    public static Credential getCredential(String password) {
        if(credential == null){
            credential = resolveCredential(password);
            //生产环境中不要打印私钥
//            log.info("私钥:");
//            log.info(EncodingUtils.encode(credential.getPrivateKey().getEncoded()));
//            log.info("公钥:");
//            log.info(EncodingUtils.encode(credential.getPublicKey().getEncoded()));
        }
        return credential;
    }
    public static Credential readCredential(InputStream inputStream) throws IOException {
        String certificate = IOUtils.toString(inputStream,"UTF-8");
        return SamlKeyStoreProvider.getCredential(null, certificate,null);
    }

}
