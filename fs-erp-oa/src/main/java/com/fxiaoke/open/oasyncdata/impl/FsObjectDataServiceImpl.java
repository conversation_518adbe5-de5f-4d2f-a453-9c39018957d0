package com.fxiaoke.open.oasyncdata.impl;

import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.FsObjectDataService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 13:59 2020/9/3
 * @Desc:
 */
@Slf4j
@Service
@Data
public class FsObjectDataServiceImpl implements FsObjectDataService {
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Override
    public Result<List<EmployeeDto>> getEmployeeFs(Integer ei) {
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(ei);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = null;
        try {
            GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setRunStatus(RunStatus.ALL);
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            GetAllEmployeeIdsResult ids = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);

            batchGetEmployeeDtoArg.setEmployeeIds(ids.getEmployeeIds());
            batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        } catch (OrganizationException e) {
            log.error("获取纷享职员信息失败，exception = {}.", e);
            return Result.newError(ResultCodeEnum.GET_FS_EMPLOYEE_FAILED);
        }

        List<EmployeeDto> employeeDtoList = batchGetEmployeeDtoResult.getEmployeeDtos();
        if(employeeDtoList==null){
            return Result.newSuccess(Lists.newArrayList());
        }else{
            return Result.newSuccess(employeeDtoList);
        }
    }
}
