package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.db.constant.CommonConstant;
import com.fxiaoke.open.oasyncdata.exsyexcel.handler.CustomCellWriteHandler;
import com.fxiaoke.open.oasyncdata.model.OAExcelFileResult;
import com.fxiaoke.open.oasyncdata.model.OAExcelSheetArg;
import com.fxiaoke.open.oasyncdata.model.OAExcelTemplateWriteHandler;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.util.OAExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/12 19:00
 * @Version 1.0
 */
@Service
@Slf4j
public class OAExcelFileManager {

    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     */
    public <R> Result<OAExcelFileResult.Result> buildExcelFileResult(I18NStringManager i18NStringManager,
                                                                     String lang,
                                                                     String tenantId,
                                                                     OAExcelFileResult.Arg<R> buildExcelArg) {
        String ea = buildExcelArg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(buildExcelArg.getTenantId()));
        }
        List<R> dataList = buildExcelArg.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new Result<>(ResultCodeEnum.LIST_EMPTY);
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = buildExcelArg.getStyle();
        if (styleStrategy == null) {
            styleStrategy = OAExcelUtils.getDefaultStyle();
        }
        ExcelWriter excelWriter = null;
        try {
            final CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, buildExcelArg.getTenantId(), lang);

            excelWriter = EasyExcel.write(outputStream, dataList.get(0).getClass())
                    .registerWriteHandler(styleStrategy)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new OAExcelTemplateWriteHandler(buildExcelArg.getFileName()))
                    .build();
            for (String sheetName : buildExcelArg.getSheetNames()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).registerWriteHandler(customCellWriteHandler).build();
                excelWriter.write(dataList, writeSheet);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        //上传文件系统
        String tnPath = this.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, "", outputStream.toByteArray());
       OAExcelFileResult.Result result = new OAExcelFileResult.Result();
        String fileName = StringUtils.appendIfMissing(buildExcelArg.getFileName(), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }


    private Pattern illegalSheetNamePattern = Pattern.compile("[:/\\\\*?]");
    private boolean checkExcelSheetNameIllegal(String name) {
//        确认输入的名称不多于31个字符。确认名称中不包含以下字符：：\/？*【或]。确认名称的第一个或者最后一个字符不能是单引号。确认工作表名称不为空。
        return StringUtils.isBlank(name) || name.startsWith("'") || name.endsWith("'") || illegalSheetNamePattern.matcher(name).find();
    }



    public String uploadTnFile(String ea, Integer userId, String fileName, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        if (!org.springframework.util.StringUtils.isEmpty(fileName)) {
            arg.setOriginName(fileName);
        }
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile get result null");
        }
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,tenantId);
    }

}
