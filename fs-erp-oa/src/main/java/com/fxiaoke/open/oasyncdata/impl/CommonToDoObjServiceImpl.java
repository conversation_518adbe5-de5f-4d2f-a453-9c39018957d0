package com.fxiaoke.open.oasyncdata.impl;

import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.service.ObjectServiceFiledConvert;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/11/18 17:33 审批流程处理字段
 * @Version 1.0
 */
@Service("commonTodoObjServiceImpl")
@Slf4j
public class CommonToDoObjServiceImpl implements ObjectServiceFiledConvert {

    @Autowired
    private UserManager userManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("oa.author.app.url")
    private String oaAuthorAppUrl;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;

    /**
     * 处理特殊字段
     * @param controllerGetDescribeResult
     * @param tenantId
     */
    @Override
    public void dealSpecialField(ControllerGetDescribeResult controllerGetDescribeResult, String tenantId,String dataCenterId) {
        //人员的描述，全部替换成对应的erp人员
        HashMap<String, FieldDescribe> fields = controllerGetDescribeResult.getDescribe().getFields();
        fields.keySet().forEach(item ->{
            switch (fields.get(item).getType()){
                case "employee":
                    Object employeeValue = controllerGetDescribeResult.getData().get(item);
                    if(ObjectUtils.isEmpty(employeeValue)){
                        break;
                    }
                    List<String> empIds = (List<String>) employeeValue;
                    //不转化。再后面在转
                    if(!item.equals("candidate_ids")){
                        List<String> erpSubmitterCodeList = userManager.getUserCodeList(empIds, tenantId,dataCenterId);
                        if(!CollectionUtils.isEmpty(erpSubmitterCodeList)){
                            //处理人节点取list
                            controllerGetDescribeResult.getData().put(item,erpSubmitterCodeList.get(0));
                        }
                    }
                    break;
                case "date_time":
                    Object dataTime = controllerGetDescribeResult.getData().get(item);
                    if(ObjectUtils.isEmpty(dataTime)){
                        break;
                    }
                    if(item.equals("create_time")){
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = new Date(((Double) dataTime).longValue());
                        String createTime = simpleDateFormat.format(date);
                        controllerGetDescribeResult.getData().put(item, createTime);
                    }
                    break;
                default:break;
            }
        });


    }

//    /**
//     * 统一处理接收人，不同的biztype标识的字段不一致
//     * @param objectData
//     * @param tenantId
//     */
//    @Override
//    public List<String> getCRMReceivers(ObjectData objectData, String tenantId) {
//        //根据消息平台的人员做处理
//        return null;
//    }

    @Override
    public Map<String,String> builderTitle(ObjectData objectData,String bizType, String tenantId,String dataCenterId) {
        //审批流的title

        HashMap<String, String> objectNameMap = Maps.newHashMap();
        objectNameMap.put("object_name",ObjectApiEnum.getObjApiEnumByBizType(bizType).getObjName(i18NStringManager,null,tenantId));
        objectNameMap.put("data_name",String.valueOf(objectData.get("name")));
        return objectNameMap;
    }


    /** 占位符特殊处理
     *
     * @param oaObjectFieldVO
     * @param data
     * @param tenantId
     * @param resultJson
     * @return
     */
    @Override
    public String specialHand(OAObjectFieldVO oaObjectFieldVO, ObjectData data, String tenantId, String resultJson,String dataCenterId) {
        if (data.get(oaObjectFieldVO.getFieldApiName()) == null || "#F001".equals(oaObjectFieldVO.getReplaceName())) {
            //因为F001是需要后续在更改
            return resultJson;
        }
        return null;
    }

    public static void main(String[] args) {
        ObjectData objectData=new ObjectData();
//        objectData.put("last_modified_time","1685161068142");
//        Long time = new Double((Double) objectData.get("last_modified_time")).longValue();
//        System.out.printf(""+time);
        objectData.put("last_modified_time","1684910163068");
       long time = new Double((Double) objectData.get("last_modified_time")).longValue();
        System.out.printf(""+time);

    }
}

