package com.fxiaoke.open.oasyncdata.db.constant;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
public enum TenantConfigurationTypeEnum {
    /**
     * 消息通知设置
     */
    messageNotification,
    /**
     * 最后消息通知时间
     */
    lastMessageNotificationTime,
    /**
     * 销售订单是否走cpq逻辑
     */
    saleOrderNeedHandleCpq,
    /**
     * 销售出库单的序列号配置
     */
    outStockSerialNumber,
    /**
     * 直接调拨单的序列号配置
     */
    transferDirectSerialNumber,
    /**
     * 销售退货单的序列号配置
     */
    returnStockSerialNumber,
    /**
     * k3商品产品是否走多单位逻辑
     */
    productNeedHandleMultipleUnit,
    /**
     * 客户和纷享的服务器之间的时间差
     */
    SERVER_TIME_DIFF,

    /**
     * 启用移除不需要同步的数据
     */
    ENABLE_REMOVE_NOT_NEED_SYNC,

    /**
     *  库存可用量的配置
     */
    INVENTORY_AVB_QTY,

    /**项目具有实施负责人*/
    EXIST_PROJECT_OWNER,

    /**
     * 不走专表的企业列表
     */
    DISABLE_DYNAMIC_TENANTS,

    /**
     * k3使用View接口获取数据详情的对象配置(旧接口）
     */
    USE_BILLQUERY_INTERFACE_TO_VIEW,

    /**
     * 单据接口按明细拆开查询
     */
    USE_BILLQUERY_INTERFACE_SPLIT_DETAIL,

    /**
     * k3对比ExecuteBillQuery接口和view接口数据
     */
    COMPARE_BILLQUERY_VIEW_RESULT,
    /**
     * 数据维护创建错误数量阈值
     */
    DATA_MAPPING_ERROR,
    /**
     * 灰度企业
     */
    GRAY_TENANTS,

    /**
     * 一天内触发策略熔断的错误数量
     */
    BREAK_FAILED_SYNC_DATA_NUM,

    /**
     * 推送数据每分钟限制条数
     */
    LIMIT_PUSH_ERP_DATA_PER_MINUTE,

    /**
     * 轮询临时库是ERP往CRM限制的倍数
     */
    DEFAULT_LIMIT_QUERY_TEMP_COEFFICIENT,

    /**
     * 批量写crm,企业和对象配置
     */
    BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME,

    /**
     * topic聚合的时间配置
     */
    MONGO_DISPATCHER_DATA_AGGREGATION_TIME,
    /**
     * 处理CRM删除事件的配置
     */
    LISTEN_CRM_DELETE_DATA,
    /**
     * OA普通环境
     */
    OA_NOR_ENV_TENANT,
    /**
     * OA灰度环境
     */
    OA_GRAY_ENV_TENANT,


    /**
     * 序列化null值的企业名单
     */
    SERIALIZE_NULL_TENANTS,
    /**
     * 企业路由配置
     */
    CONFIG_ROUTE_TENANT,
}
