package com.fxiaoke.open.oasyncdata.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2022/7/28 16:36 用来记录messageId
 * @Version 1.0
 */
@Slf4j
public class MsgUtil {
    private static final TransmittableThreadLocal<String> BASE_LOG = new TransmittableThreadLocal<>();
    public static void setBaseLog(String messageId) {
        BASE_LOG.set(messageId);
    }
    public static String getBaseLog() {
       return BASE_LOG.get();
    }
    /**
     * 在线程结束前调用防止内存泄露
     */
    public static void clear() {
        BASE_LOG.remove();
    }
}
