package com.fxiaoke.open.oasyncdata.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.open.oasyncdata.constant.OAMessageTag;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.ExternalMsgManager;
import com.fxiaoke.open.oasyncdata.manager.ExternalToDoManager;
import com.fxiaoke.open.oasyncdata.util.BizLogUtils;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/11/8 17:03 针对CRM的审批流，代办流同步消息消费
 * @Version 1.0
 */
@Service("crmOAMessageListener")
@Slf4j
public class CRMOAMessageListener implements MessageListenerConcurrently {

    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private ExternalMsgManager externalMsgManager;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    private static boolean openDebugLog = false;

    public static void changeOpenDebugLog() {
        openDebugLog = !openDebugLog;
        log.info("CRMOAMessageListener changeOpenDebugLog:{}", openDebugLog);
    }


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        if (openDebugLog) {
            //打印所有接收到的msg
            String msgJoining = list.stream().map(msg -> StrUtil.join(",", msg.getMsgId(), msg.getFlag(), msg.getTags())).collect(Collectors.joining(";"));
            log.info("crmOAMessageListener consume msg,size:{},msgs:{}", list.size(), msgJoining);
        }
        for (MessageExt msg : list) {
            try {
                MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                String receiveTags = msg.getTags();
                String tenantId = MessageHelper.getTenantId(msg);
                List<OAConnectInfoEntity> connectInfos = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
                if (CollUtil.isEmpty(connectInfos)) {
                    //无连接信息，不处理
                    continue;
                }
                //格式化的traceId
                TraceUtil.initTraceWithFormat(tenantId,TraceContext.get().getUid(),msg.getMsgId());
                BizLogUtils.sendOADealWithMsgLog(tenantId,"crmOAMessageListener",receiveTags);
                switch (receiveTags) {
                        case OAMessageTag.CREATE_TO_DO_TAG:
                            CreateTodoArg createTodoArg = JSONObject.parseObject(msg.getBody(), CreateTodoArg.class);
                            externalToDoManager.createTodo(createTodoArg,false,null);
                            break;
                        case OAMessageTag.DEAL_TO_DO_TAG:
                            DealTodoArg dealTodoArg
                                    = JSONObject.parseObject(msg.getBody(), DealTodoArg.class);
                            externalToDoManager.dealTodo(dealTodoArg,false,null);
                            break;
                        case OAMessageTag.DELETE_TO_DO:
                            DeleteTodoArg deleteTodoArg
                                    = JSONObject.parseObject(msg.getBody(), DeleteTodoArg.class);
                            externalToDoManager.deleteTodo(deleteTodoArg,false,null);
                            break;
                        case OAMessageTag.TEXT_MSG_TAG:
                            SendTextMessageArg sendTextMessageArg=JSONObject.parseObject(msg.getBody(), SendTextMessageArg.class);
                            externalMsgManager.sendTextMessage(sendTextMessageArg,null);
                            break;
                        case OAMessageTag.CARD_MSG_TAG:
                            SendTextCardMessageArg sendTextCardMessageArg=JSONObject.parseObject(msg.getBody(), SendTextCardMessageArg.class);
                            externalMsgManager.sendTextCardMessage(sendTextCardMessageArg,null);
                            break;
                        default:
                            break;

                    }
            } catch (Exception e) {
                log.error("CRMOAMessageListener consume  failed.", e);
                TraceUtil.removeTrace();
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }finally {
                TraceContext.remove();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
