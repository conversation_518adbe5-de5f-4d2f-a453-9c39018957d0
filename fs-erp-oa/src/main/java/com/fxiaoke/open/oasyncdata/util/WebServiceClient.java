package com.fxiaoke.open.oasyncdata.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.dom4j.DocumentException;

import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @Date 2021/9/24 20:02
 * @Version 1.0
 */
@Slf4j
public class WebServiceClient {

    private final static String CONTENT_TYPE_TEXT_JSON = "text/json";

    public static String doPostSoap(String url, String soap, String SOAPAction) {
        //请求体
        String retStr = "";
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        // HttpClient
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
            httpPost.setHeader("SOAPAction", SOAPAction);
            StringEntity data = new StringEntity(soap,
                    Charset.forName("UTF-8"));
            httpPost.setEntity(data);
            CloseableHttpResponse response = closeableHttpClient
                    .execute(httpPost);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                // 打印响应内容
                retStr = EntityUtils.toString(httpEntity, "UTF-8");
                System.err.println("response:" + retStr);
            }
            // 释放资源
            closeableHttpClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retStr;
    }

    public static void main(String[] args) throws DocumentException, IOException {

        URL oaUrl=new URL("https://oawzjc.qilu-pharma.com:85/services/OfsTodoDataWebService?wsdl");
        URLConnection urlConnection = oaUrl.openConnection();
        Object content = urlConnection.getContent();


        String url="https://oawzjc.qilu-pharma.com:85/services/OfsTodoDataWebService";
        String parameter="<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
                "    <SOAP-ENV:Body>\n" +
                "        <m:receiveTodoRequestByJson xmlns:m=\"webservices.ofs.weaver.com.cn\">\n" +
                "            <m:in0>\n" +
                "                {\n" +
                "                    \"syscode\":\"fxiaoke\",\n" +
                "                    \"nodename\":\"#F026\",\n" +
                "                    \"requestname\":\"#F056\",\n" +
                "                    \"creator\":\"#F049\",\n" +
                "                    \"receiver\":\"#F001\",\n" +
                "                    \"workflowname\":\"#F050\",\n" +
                "                    \"pcurl\":\"#webUrl\",\n" +
                "                    \"appurl\":\"#appUrl\",\n" +
                "                    \"receivedatetime\":\"#F009\",\n" +
                "                    \"createdatetime\":\"#F009\",\n" +
                "                    \"flowid\":\"#F012\"\n" +
                "                }\n" +
                "            </m:in0>\n" +
                "        </m:receiveTodoRequestByJson>\n" +
                "    </SOAP-ENV:Body>\n" +
                "</SOAP-ENV:Envelope";
        String result = WebServiceClient.doPostSoap(url, parameter, "");
        JSONObject jsonObject = WebServiceUnit.xml2Json(result);
        log.info("result:{}");
    }

}
