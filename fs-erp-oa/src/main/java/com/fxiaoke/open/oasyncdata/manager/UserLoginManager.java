package com.fxiaoke.open.oasyncdata.manager;

import com.facishare.appserver.auditlog.api.bean.LoginLogMessage;
import com.facishare.appserver.auditlog.util.LoginLogUtil;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.util.ContactsUtils;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.Date;

/**
 * 用户登录相关
 *
 * <AUTHOR>
 * @date 2021/1/12
 */
@Component
@Slf4j
public class UserLoginManager {
    @Autowired
    private SSOLoginService ssoLoginService;

    @ReloadableProperty("sso.redirect.url")
    private String ssoRedirectUrl;

    @ReloadableProperty("sso.source.web.url")
    private String ssoRedirectWebUrl;

    @ReloadableProperty("sso.source.detail.h5.url")
    private String ssoSourceDetailH5Url;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private OASettingService oaSettingService;

    /**
     * OA单点登录业务key，用于记录单点登录日志
     */
    private static final String OA_SSO_BIZ_KEY = "erpdss_oa";


    /**
     * 跳转具体单据页面
     *
     * @param ei
     * @param fsUserId
     * @param apiName
     * @param dataId
     * @param isApp
     * @return
     */
    public Result<String> avoidLogin(Integer ei, String fsUserId, String apiName, String dataId, boolean isApp, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,i18NStringManager.get(I18NStringEnum.s1261,null,ei+""),request);
            return Result.newErrorByI18N(ResultCodeEnum.RESULT_NULL,
                    I18NStringEnum.s1261.getI18nKey(),
                    I18NStringEnum.s1261.getI18nKey(),
                    null);
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        String sourceUrl;
        if (isApp) {
            sourceUrl = getH5ObjectRedirectUrl(ei) + "?id=" + dataId + "&apiname=" + apiName;
        } else {
            sourceUrl = ssoRedirectWebUrl + apiName + "/" + dataId;
        }
        String finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(finalUrl);
    }

    public Result<String> avoidLogin(Integer ei, String fsUserId, String redirectCRMUrl, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            String msg=i18NStringManager.getByEi2(I18NStringEnum.s1262.getI18nKey(),
                    ei+"",
                    String.format(I18NStringEnum.s1262.getI18nValue(), ei,fsUserId),
                    Lists.newArrayList(ei+"",fsUserId));
            recordLoginLog(ei+"",fsUserId,false,msg,request);
            return Result.newErrorByI18N(ResultCodeEnum.RESULT_NULL, msg,null,null);
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        //正则匹配自定义域名。
        String domainUrl = replaceDomain(String.valueOf(ei),redirectUrl);
        String finalUrl = domainUrl + "&source=" + URLEncoder.encode(redirectCRMUrl);
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(finalUrl);
    }


    /**
     * 跳转主页面
     *
     * @param ei
     * @param fsUserId
     * @param isApp
     * @return
     */
    public Result<String> avoidLoginMainPage(Integer ei, String fsUserId, boolean isApp, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,"Get token fail!",request);
            return Result.newError(ResultCodeEnum.RESULT_NULL, "Get token fail!");
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        if (isApp) {
            redirectUrl = redirectUrl + "&source=" + URLEncoder.encode(getH5PageRedirectUrl(ei));
        }
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(redirectUrl);
    }

    public String replaceDomain(String tenantId,String ssoRedirectUrl){
        Result<OASettingVO> settingInfo = oaSettingService.getSettingInfo(tenantId, OATenantEnum.OA_SUPPORT_CUSTOM_DOMAIN, "*");
        if(settingInfo.isSuccess()&& ObjectUtils.isNotEmpty(settingInfo.getData())){
            String url= settingInfo.getData().getConfiguration();//http://xxxx.fxiaoke.com/
            String convertUrl = ssoRedirectUrl.replaceAll(".*/FHH", url);
            return convertUrl;

        }
        return ssoRedirectUrl;
    }

    /**
     * 跳转到待办列表页面
     * @param ei
     * @param fsUserId
     * @param isApp
     * @return
     */
    public Result<String> avoidLoginListPage(Integer ei, String fsUserId, Boolean isApp, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,"Get token fail!",request);
            return Result.newError(ResultCodeEnum.RESULT_NULL, "Get token fail!");
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        if (isApp) {
            redirectUrl = redirectUrl + "&source=" + URLEncoder.encode("/hcrm/qihoo#/crm/remind");
        }else {
            redirectUrl = redirectUrl + "&source=" + URLEncoder.encode("/XV/Home/Index#crm/remind/approvalbyme");
        }
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(redirectUrl);
    }

    /**
     * 360跳转链接
     * @param ei
     * @param fsUserId
     * @param apiName
     * @param dataId
     * @param isApp
     * @return
     */
    public Result<String> avoidLoginForQihoo(Integer ei, String fsUserId, String apiName, String dataId, boolean isApp, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,"Get token fail!",request);
            return Result.newError(ResultCodeEnum.RESULT_NULL, "Get token fail!");
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        String sourceUrl;
        if (isApp) {
            sourceUrl = "/hcrm/qihoo#/crm/detail" + "?id=" + dataId + "&apiname=" + apiName;
            //sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;
        } else {
            //目前只有app跳转 360不支持pc跳转 oa配置isapp为true
            sourceUrl = ssoRedirectWebUrl + apiName + "/" + dataId;
        }
        String finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(finalUrl);
    }

    /**
     * 跳转具体对象页面
     * @param ei
     * @param fsUserId
     * @param apiName
     * @return
     */
    public Result<String> avoidLoginObjectPage(Integer ei, String fsUserId, String apiName, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,"Get token fail!",request);
            return Result.newError(ResultCodeEnum.RESULT_NULL, "Get token fail!");
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        String sourceUrl = "/XV/Home/Index#crm/list/=/" + apiName;
        String finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(finalUrl);
    }

    public Result<String> avoidLoginPage(Integer ei, String fsUserId, String url, HttpServletRequest request) {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", ssoResult);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
            recordLoginLog(ei+"",fsUserId,false,"Get token fail!",request);
            return Result.newError(ResultCodeEnum.RESULT_NULL, "Get token fail!");
        }
        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        String finalUrl = redirectUrl + "&source=" + URLEncoder.encode(url);
        recordLoginLog(ei+"",fsUserId,true,null,request);
        return Result.newSuccess(finalUrl);
    }

    public Result<Void> recordLoginLog(String tenantId,
                                       String userId,
                                       boolean loginSuccess,
                                       String errMsg,
                                       HttpServletRequest request) {
        LoginLogMessage msg = new LoginLogMessage();
        //msg.setUniqueId(OA_SSO_BIZ_KEY);
        msg.setTenantId(tenantId);
        msg.setAppId("CRM");
        msg.setLoginType("5");// 单点登录
        msg.setLoginStatus(loginSuccess ? "1" : "2");
        if(!loginSuccess) {
            msg.setException(errMsg);
        }
        String userAgent = request.getHeader("user-agent");
        log.info("UserLoginManager.recordLoginLog,userAgent={}",userAgent);
        if(StringUtils.isNotEmpty(userAgent)) {
            UserAgent userAgentObj = UserAgent.parseUserAgentString(userAgent);
            msg.setLoginBrowser(userAgentObj.getBrowser().getName());
        }
        msg.setLoginTime(System.currentTimeMillis());
        msg.setLoginIp(request.getRemoteHost());
        msg.setOperationTime(new Date());

        msg.setUserId(userId);
        msg.setOwnerId(userId);
        EmployeeDto employeeInfo = ContactsUtils.getEmployeeInfo(tenantId, userId, employeeProviderService);
        if(employeeInfo!=null) {
            msg.setUserName(employeeInfo.getName());
            msg.setOwnerName(employeeInfo.getName());
            msg.setDeptId(employeeInfo.getMainDepartmentId()+"");

            DepartmentDto departmentDto = ContactsUtils.getDepInfo(tenantId,
                    employeeInfo.getMainDepartmentId()+"",
                    departmentProviderService);
            if(departmentDto!=null) {
                msg.setDeptName(departmentDto.getName());
            }
        }

        log.info("UserLoginManager.recordLoginLog,msg={}",msg);
        LoginLogUtil.log(msg);

        return Result.newSuccess();
    }

    //对象页面
    private String getH5ObjectRedirectUrl(Integer ei){
        if(ConfigCenter.OA_REDIRECT_AVAH5_TENANT.contains(String.valueOf(ei))){
            return ConfigCenter.OA_AVAH5_OBJECT_REDIRECT_URL;
        }
        return ssoSourceDetailH5Url;
    }
    //首页
    private String getH5PageRedirectUrl(Integer ei){
        if(ConfigCenter.OA_REDIRECT_AVAH5_TENANT.contains(String.valueOf(ei))){
            return ConfigCenter.OA_AVAH5_PAGE_REDIRECT_URL;
        }
        return ConfigCenter.OA_DINGTALK_PAGE_REDIRECT_URL;
    }
}
