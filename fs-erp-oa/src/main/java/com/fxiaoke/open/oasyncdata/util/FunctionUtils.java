package com.fxiaoke.open.oasyncdata.util;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.CustomFunctionConstant;
import com.fxiaoke.open.oasyncdata.constant.CustomFunctionConstantEx;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.manager.FunctionApiManager;
import com.fxiaoke.open.oasyncdata.model.ObjectData;
import com.fxiaoke.open.oasyncdata.result.Result2;
import com.fxiaoke.open.oasyncdata.result.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class FunctionUtils {

    @Autowired
    private FunctionApiManager functionApiManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 执行APL函数
     * @param ei
     * @param aplApiName
     * @param dataValueMap APL函数入参
     * @return
     */
    public Result<Map<String,Object>> executeFunction(String ei, String aplApiName, Map<String, Object> dataValueMap, OASyncLogSnapshotDoc oaSyncLogSnapshotDoc){
        List<FunctionServiceParameterData> functionServiceParameterDataList = new ArrayList<>();
        FunctionServiceParameterData<Map> functionServiceParameterData = new FunctionServiceParameterData();
        functionServiceParameterData.setName(CustomFunctionConstant.SYNC_ARG_NAME);
        functionServiceParameterData.setType(CustomFunctionConstant.SYNC_ARG_TYPE_MAP);
        functionServiceParameterData.setValue(dataValueMap);
        functionServiceParameterDataList.add(functionServiceParameterData);

        FunctionServiceExecuteArg functionServiceExecuteArg = new FunctionServiceExecuteArg();
        functionServiceExecuteArg.setApiName(aplApiName);
        functionServiceExecuteArg.setNameSpace(CustomFunctionConstantEx.NAME_SPACE);
        functionServiceExecuteArg.setBindingObjectAPIName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        functionServiceExecuteArg.setParameters(functionServiceParameterDataList);
        HeaderObj headerObj = I18NHeaderObj.getHeader2(ei+"",i18NStringManager);

        if(oaSyncLogSnapshotDoc!=null) {
            oaSyncLogSnapshotDoc.setAplApiName(aplApiName);
            oaSyncLogSnapshotDoc.setUrl(null);
            oaSyncLogSnapshotDoc.setMethod("POST");
            oaSyncLogSnapshotDoc.setHeader(JSONObject.toJSONString(headerObj));
            oaSyncLogSnapshotDoc.setBody(JSONObject.toJSONString(functionServiceExecuteArg));
        }

        ObjectData objectData= functionApiManager.executeCustomFunction(headerObj ,functionServiceExecuteArg,false);
        log.info("FunctionUtils.executeFunction,objectData={}",objectData);

        if(oaSyncLogSnapshotDoc!=null) {
            oaSyncLogSnapshotDoc.setResponse(objectData==null ? "" : JSONObject.toJSONString(objectData));
        }

        if(objectData==null) {
            return Result.newSystemError(I18NStringEnum.s1258);
        }
        int errCode = objectData.getInt("errCode");
        String errMessage = objectData.getString("errMessage");
        Map<String,Object> resultMap = objectData.getMap("result");

        Result2<Map<String,Object>> result = Result2.newError4CustomFunc(errCode,errMessage);

        Boolean success = MapUtils.getBoolean(resultMap,"success");
        String errorInfo = MapUtils.getString(resultMap,"errorInfo");

        if(success==false) {
            List<String> items = Splitter.on(":::").splitToList(errorInfo);
            if(CollectionUtils.isNotEmpty(items) && items.size()==2) {
                int code = 0;
                try {
                    code = Integer.valueOf(items.get(0));
                } catch (Exception e) {
                    code = errCode;
                }
                return Result2.newError(code,items.get(1));
            }
        }

        if(success!=null && success==false) {
            result.setIntErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            result.setErrMsg(errorInfo);
            return result;
        }
        Map<String,Object> functionResultMap;
        try {
            functionResultMap = (Map<String, Object>) resultMap.get("functionResult");
        } catch (Exception e) {
            throw new ErpSyncDataException(I18NStringEnum.s1252,ei);
        }

        if(functionResultMap==null) {
            result.setIntErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s1254, ei));
            return result;
        }

        return Result.newSuccess(functionResultMap);
    }

    /**
     * 执行待办自定义函数
     * @param tenantId
     * @param todoAplApiName 自定义函数名称
     * @param requestJson 传给自定义函数的数据，放在request_data字段中
     * @return
     */
    public Result<String> getRequestData(String tenantId,String todoAplApiName,String requestJson,OASyncLogSnapshotDoc oaSyncLogSnapshotDoc) {
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("request_data",requestJson);
        Result<Map<String, Object>> functionResult = executeFunction(tenantId, todoAplApiName, dataMap, oaSyncLogSnapshotDoc);
        log.info("CustomFuncManager.getRequestData,functionResult={}",functionResult);
        if(functionResult.isSuccess()==false) {
            return Result.newError(functionResult.getErrCode(),functionResult.getErrMsg());
        }
        Object requestData = functionResult.getData().get("request_data");
        log.info("CustomFuncManager.getRequestData,requestData={}",requestData);
        if(Objects.isNull(requestData)) {
            return Result.newError(com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum.CUSTOM_FUNC_ERROR.getErrCode(),
                    I18NStringEnum.s3664);
        }
        return new Result<>(requestData.toString());
    }


}
