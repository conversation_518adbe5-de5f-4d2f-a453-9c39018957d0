package com.fxiaoke.open.oasyncdata.impl;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.db.dao.OAFlowMqConfigDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.model.OAFlowMqConfigResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAFlowMqConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class OAFlowMqConfigSeriveImpl implements OAFlowMqConfigService {

    @Autowired
    private OAFlowMqConfigDao oaFlowMqConfigDao;

    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<List> addOrUpdate(String tenantId, List<OAFlowMqConfigResult> results, String lang) {
        List<String> result = new ArrayList<>();
        for (OAFlowMqConfigResult oaFlowMqConfigResult : results) {
            int i;
//            if(!tenantId.equals(oaFlowMqConfigEntity.getTenantId())){
//                result.add(oaFlowMqConfigEntity+ "操作失败！企业ID不匹配！");
//            }
            OAFlowMqConfigEntity oaFlowMqConfigEntity= BeanUtil.deepCopy(oaFlowMqConfigResult,OAFlowMqConfigEntity.class);
            oaFlowMqConfigEntity.setTenantId(tenantId);
            if(StringUtils.isBlank(oaFlowMqConfigEntity.getId())){
                String id = idGenerator.get();
                oaFlowMqConfigEntity.setId(id);
                i = oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaFlowMqConfigEntity);
            }else{
                i = oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaFlowMqConfigEntity);
            }
            if(i == 1){
                result.add(oaFlowMqConfigEntity +"," + i18NStringManager.get(I18NStringEnum.s1244,lang,tenantId));
            }else{
                result.add(oaFlowMqConfigEntity +"," + i18NStringManager.get(I18NStringEnum.s1245,lang,tenantId));
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<OAFlowMqConfigResult>> list(String tenantId) {
        OAFlowMqConfigEntity oaFlowMqConfigEntity = new OAFlowMqConfigEntity();
        oaFlowMqConfigEntity.setTenantId(tenantId);
        List<OAFlowMqConfigEntity> oaFlowMqConfigEntities = oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(oaFlowMqConfigEntity);
        List<OAFlowMqConfigResult> oaFlowMqConfigResults = BeanUtil.deepCopyList(oaFlowMqConfigEntities, OAFlowMqConfigResult.class);
        return Result.newSuccess(oaFlowMqConfigResults);
    }

    @Override
    public Result<String> deleteById(String tenantId, Map params, String lang) {
        String id = String.valueOf(params.get("id"));
        OAFlowMqConfigEntity oaFlowMqConfigEntity =  oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(id);
        if(!tenantId.equals(oaFlowMqConfigEntity.getTenantId())){
            return Result.newErrorByI18N(id + i18NStringManager.get(I18NStringEnum.s1253,lang,tenantId),null,null);
        }
        int i = oaFlowMqConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(id);
        if(i == 1){
            return Result.newSuccess(i18NStringManager.get(I18NStringEnum.s1245,lang,tenantId));
        }
        return Result.newErrorByI18N(id + i18NStringManager.get(I18NStringEnum.s352,lang,tenantId),null,null);
    }
}
