package com.fxiaoke.open.oasyncdata.mq;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.manager.OAFlowManager;
import com.fxiaoke.open.oasyncdata.util.BizLogUtils;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Service("crmApprovalMessageListener")
@Slf4j
public class CRMApprovalMessageListener implements MessageListenerConcurrently {

    @Autowired
    private OAFlowManager oaFlowManager;

    private static ExecutorService  executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("crmApprovalMessageListener-%d").build();
        executorService = new ThreadPoolExecutor(10, 30, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        for (MessageExt msg : msgs) {
            try {
                MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                JSONObject jsonObject = JSONObject.parseObject(msg.getBody(), JSONObject.class);
                Map<String, Object> body = GsonUtil.fromJson(jsonObject.toJSONString(), Map.class);
                String eventType = String.valueOf(body.get("eventType"));
                Map<String, Object> eventData = (Map<String, Object>) body.get("eventData");
                String tenantId = String.valueOf(eventData.get("tenantId"));
                boolean hasSetting=queryGrayTenantIds(tenantId);
                if(!hasSetting){
                    //没有配置企业的，跳过
                    continue;
                }
                BizLogUtils.sendOADealWithMsgLog(tenantId,"crmApprovalMessageListener",null);
                //查询数据
                OAFlowMqConfigEntity oaFlowMqConfigEntity = new OAFlowMqConfigEntity();
                oaFlowMqConfigEntity.setEventType(eventType);
                oaFlowMqConfigEntity.setTenantId(tenantId);
                oaFlowMqConfigEntity.setObjApiName(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName());
                List<OAFlowMqConfigEntity> configEntities = oaFlowManager.queryList(tenantId,oaFlowMqConfigEntity);
                if (configEntities.size() > 0) {
                    //数量大于1则打log警告
                    if (configEntities.size() > 1) {
                        log.warn("configEntities :{}", configEntities);
                    }
                    //存在相应配置，消费消息
                    OAFlowMqConfigEntity config = configEntities.get(0);
                    executorService.submit(()->{
                        log.info("crmApprovalMessageListener consume msgId:{}", msg.getMsgId());
                        oaFlowManager.handle(body, config);
                    });
                }
            } catch (Exception e) {
                log.error("crmApprovalMessageListener consume  failed. e:{}", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }finally {
                TraceContext.remove();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    public boolean queryGrayTenantIds(String tenantId){
        List<String> tenantIdList = oaFlowManager.queryTenantIdList();
        if(tenantIdList.contains(tenantId)){
            return true;
        }
        return false;
    }
}
