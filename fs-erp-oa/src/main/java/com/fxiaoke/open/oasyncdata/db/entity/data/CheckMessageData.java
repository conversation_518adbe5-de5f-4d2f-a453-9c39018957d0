package com.fxiaoke.open.oasyncdata.db.entity.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14:59 2021/11/11
 * @Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckMessageData implements Serializable {
    /**
     * type=1,依赖的数据
     */
    public String dependSourceApiName;
    public String dependDataId;
    public String dependDestApiName;

    /**
     * type=2,超时重试的数据
     */
    public String doWriteDataJson;
    public Boolean needReSync=false;
}
