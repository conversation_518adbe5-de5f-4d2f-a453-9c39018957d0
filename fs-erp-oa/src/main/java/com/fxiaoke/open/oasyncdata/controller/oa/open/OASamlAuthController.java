package com.fxiaoke.open.oasyncdata.controller.oa.open;


import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.UserLoginManager;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.saml.OpenSamlImplementation;
import com.fxiaoke.open.oasyncdata.service.OAUserAuthService;
import com.fxiaoke.open.oasyncdata.util.EncodingUtils;
import com.fxiaoke.otherrestapi.eservice.common.util.UUIDUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.shibboleth.utilities.java.support.component.ComponentInitializationException;
import org.apache.commons.lang.StringUtils;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.joda.time.DateTime;
import org.opensaml.core.xml.io.MarshallingException;
import org.opensaml.core.xml.schema.XSAny;
import org.opensaml.core.xml.schema.impl.XSAnyBuilder;
import org.opensaml.messaging.context.MessageContext;
import org.opensaml.messaging.encoder.MessageEncodingException;
import org.opensaml.saml.common.SAMLVersion;
import org.opensaml.saml.common.messaging.context.SAMLBindingContext;
import org.opensaml.saml.common.messaging.context.SAMLEndpointContext;
import org.opensaml.saml.common.messaging.context.SAMLPeerEntityContext;
import org.opensaml.saml.common.xml.SAMLConstants;
import org.opensaml.saml.saml2.binding.encoding.impl.HTTPPostEncoder;
import org.opensaml.saml.saml2.core.*;
import org.opensaml.saml.saml2.core.impl.LogoutRequestImpl;
import org.opensaml.saml.saml2.metadata.*;
import org.opensaml.security.SecurityException;
import org.opensaml.security.credential.UsageType;
import org.opensaml.xmlsec.keyinfo.KeyInfoGenerator;
import org.opensaml.xmlsec.signature.support.SignatureConstants;
import org.opensaml.xmlsec.signature.support.SignatureException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;

import static org.opensaml.saml.common.xml.SAMLConstants.SAML2_POST_BINDING_URI;
import static org.springframework.util.StringUtils.hasText;

/**
 * <AUTHOR>
 * @date 2021/12/10
 */
@Slf4j
@Api(tags = "支持纷享Saml协议")
@Controller("oASamlAuthController")
@RequestMapping("erp/syncdata/open/saml")
public class OASamlAuthController {
    @Autowired
    private OAUserAuthService oaUserAuthService;

    @Autowired
    private UserLoginManager userLoginManager;

    @Autowired
    private UserManager userManager;
    @ReloadableProperty("sso.redirect.url")
    private String ssoRedirectUrl;

    @ReloadableProperty("sso.source.web.url")
    private String ssoRedirectWebUrl;

    @ReloadableProperty("sso.source.detail.h5.url")
    private String ssoSourceDetailH5Url;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;
    @Value("${uss.oauthRedirectUrl}")
    String oauthRedirectUrl;

    @Autowired
    private OpenSamlImplementation openSamlImplementation;

    /**
     * Velocity 引擎
     */
    private VelocityEngine velocityEngine;

    @PostConstruct
    public void initVelocity() {
        velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.ENCODING_DEFAULT, "UTF-8");
        //velocityEngine.setProperty(RuntimeConstants.OUTPUT_ENCODING, "UTF-8");
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        velocityEngine.setProperty("classpath.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        velocityEngine.init();
    }


    /**
     * 提供给纷享的Uss登录入口
     * @param samlxml
     * @param relayState
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/sso")
    public void sso(@RequestParam(value = "SAMLRequest",required = false) String samlxml,
                    @RequestParam(value = "RelayState",required = false)String relayState,
                    HttpServletRequest request,
                    HttpServletResponse response) throws Exception {
        TraceUtil.initTraceWithFormat("730173",null,null);//固定为锐捷
        log.info("SAMLRequest:[{}] RelayState[{}]",samlxml,relayState);
        String samlRequest = EncodingUtils.inflate(EncodingUtils.decode(samlxml));
        log.info("SAMLRequest:{}", samlRequest);
        AuthnRequest authnRequest = (AuthnRequest) openSamlImplementation.transferXML2SAMLObject(samlRequest);
        String requestId = authnRequest.getID();
        // 返回免登跳转url
        Result<String> url = oaUserAuthService.getRuiJieRedirectUrl(requestId);
        log.info("UssOauthLoginUrl:{}",url);
        response.sendRedirect(url.getData());

    }


    @RequestMapping("/oauth2code")
    public void oauth2codeCallBack(@RequestParam(value = "ticket",required = false) String code, @RequestParam(value = "state",required = false) String state,
                                   HttpServletRequest request, HttpServletResponse response) throws Exception {
        TraceUtil.initTraceWithFormat("730173",null,null);//固定为锐捷
        log.info("Uss oauth2codeCallBack code[{}],state[{}]", code,  state);
        Map<String, String> attributeMap = Maps.newHashMap();
        state=UUIDUtil.getUUID().replace("-","").trim();
        String userId="";
        //
        if(StringUtils.isEmpty(code)){
            //跳转到锐捷登录页
            Result<String> ruiJieRedirectUrl = oaUserAuthService.getRuiJieRedirectUrl(state);
            response.sendRedirect(ruiJieRedirectUrl.getData());
        }
        if (StringUtils.isNotEmpty(code)) {
            Result<Map<String, String>> mapResult = new Result();
            mapResult = oaUserAuthService.getRuiJieInfo(code, oauthRedirectUrl);
            if (mapResult.getErrCode() == ResultCodeEnum.SUCCESS.getErrCode()) {
                attributeMap.put("name", mapResult.getData().get("userName"));
                attributeMap.put("email", mapResult.getData().get("email"));
                attributeMap.put("phone", mapResult.getData().get("tel"));
                attributeMap.put("grender", mapResult.getData().get("userName"));
                attributeMap.put("employeeNumber", mapResult.getData().get("employeeNumber"));
                userId=mapResult.getData().get("employeeNumber");
                attributeMap.put("post", "");
            }
        }
        log.info("oauth get result:{}", JSONObject.toJSON(attributeMap));
        Response samlResponse = buildResponse(userId, attributeMap, state, OpenSamlImplementation.generateSecureRandomId());
        log.info("saml:response:{}",samlResponse);
        //对Reponse加签
        openSamlImplementation.signObject(samlResponse, SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA256,
                SignatureConstants.ALGO_ID_DIGEST_SHA256);
        httpPostBinding(ConfigCenter.RUI_JIE_SSO, response, ConfigCenter.RUI_JIE_SSO, samlResponse);
    }
    @RequestMapping("/logout")
    public void logout(@RequestParam(value = "SAMLRequest",required = false) String samlxml,
                       @RequestParam(value = "RelayState",required = false)String relayState,
                       HttpServletRequest request,
                       HttpServletResponse response) throws Exception {
        TraceUtil.initTraceWithFormat("730173",null,null);//固定为锐捷
        log.info("SAMLRequest:[{}] RelayState[{}]",samlxml,relayState);
        String samlRequest = EncodingUtils.inflate(EncodingUtils.decode(samlxml));
        log.info("SAMLRequest:{}", samlRequest);
        LogoutRequestImpl authnRequest = (LogoutRequestImpl) openSamlImplementation.transferXML2SAMLObject(samlRequest);
//        DateTime issueInstant = authnRequest.getIssueInstant();
//        String issuer = authnRequest.getIssuer().getValue();
        String requestId = authnRequest.getID();
        //TODO 返回免登跳转url
        Result<String> url = oaUserAuthService.getRuiJieLogOutUrl(requestId);
        log.info("UssOauthLoginUrl:{}",url);
        response.sendRedirect(url.getData());

    }

    /**
     * fxiaoke免登录的代码
     *
     * @param loginName
     * @param attributeMap
     * @param reqId
     * @param messageId
     * @return
     */
    private Response buildResponse(String loginName, Map<String, String> attributeMap, String reqId, String messageId) {

        Assertion assertion = openSamlImplementation.buildSAMLObject(Assertion.class);
        DateTime now = new DateTime();
        // 断言相关,随便生成的字符串
        assertion.setID(messageId);
        //必须元素,代表要登录fx平台的账号主体
        Subject subject = openSamlImplementation.buildSAMLObject(Subject.class);
        //必须元素,代表要登录的账号主要的用户名
        NameID nameID = openSamlImplementation.buildSAMLObject(NameID.class);
        nameID.setValue(loginName);
        nameID.setFormat(NameIDType.PERSISTENT);
        subject.setNameID(nameID);
        //必须元素 SubjectConfirmationData 的 Method统一为 METHOD_BEARER
        SubjectConfirmation subjectConfirmation = openSamlImplementation.buildSAMLObject(SubjectConfirmation.class);
        SubjectConfirmationData subjectConfirmationData = openSamlImplementation.buildSAMLObject(SubjectConfirmationData.class);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(reqId)) {
            subjectConfirmationData.setInResponseTo(reqId);
        }
        subjectConfirmationData.setNotOnOrAfter(now.plusMinutes(5));
        //Recipient设置域名
        subjectConfirmationData.setRecipient(ConfigCenter.RUI_JIE_SSO);
        subjectConfirmation.setSubjectConfirmationData(subjectConfirmationData);
        subjectConfirmation.setMethod(SubjectConfirmation.METHOD_BEARER);
        subject.getSubjectConfirmations().add(subjectConfirmation);

        assertion.setSubject(subject);
        assertion.getAuthnStatements().add(getAuthnStatement(messageId));
        assertion.setIssueInstant(now);
        //issuer的值与entityId一致 必须元素
        assertion.setIssuer(idpBuildIssuer());
        assertion.setIssueInstant(now);
        //必须元素
        Conditions conditions = openSamlImplementation.buildSAMLObject(Conditions.class);
        conditions.setNotBefore(now);
        conditions.setNotOnOrAfter(now.plusSeconds(5));
        AudienceRestriction audienceRestriction = openSamlImplementation.buildSAMLObject(AudienceRestriction.class);
        //必须元素
        Audience audience = openSamlImplementation.buildSAMLObject(Audience.class);
        //固定-------
        audience.setAudienceURI(ConfigCenter.SP_ENTITY_ID);
        audienceRestriction.getAudiences().add(audience);
        conditions.getAudienceRestrictions().add(audienceRestriction);
        assertion.setConditions(conditions);


        AttributeStatement attributeStatement =  openSamlImplementation.buildSAMLObject(AttributeStatement.class);
        attributeMap.keySet().stream().forEach( key -> {
            Attribute attribute = openSamlImplementation.buildSAMLObject(Attribute.class);
            attribute.setName("urn:mace:dir:attribute-def:".concat(key));
            XSAny attributeValue = new XSAnyBuilder().buildObject(AttributeValue.DEFAULT_ELEMENT_NAME);
            attributeValue.setTextContent(attributeMap.get(key));
            attribute.getAttributeValues().add(attributeValue);
            attributeStatement.getAttributes().add(attribute);
        });
        assertion.getAttributeStatements().add(attributeStatement);

        Response response = openSamlImplementation.buildSAMLObject(Response.class);
        response.setID(OpenSamlImplementation.generateSecureRandomId());
        Status status = openSamlImplementation.buildSAMLObject(Status.class);
        StatusCode statusCode = openSamlImplementation.buildSAMLObject(StatusCode.class);
        //Status Code 要设置成SUCEESS
        statusCode.setValue(StatusCode.SUCCESS);
        status.setStatusCode(statusCode);

        response.setStatus(status);
        response.setDestination(ConfigCenter.RUI_JIE_SSO);
        response.setInResponseTo(reqId);
        response.getAssertions().add(assertion);
        response.setIssueInstant(now);
        response.setIssuer(this.idpBuildIssuer());
        response.setVersion(SAMLVersion.VERSION_20);
        //对断言加签
        openSamlImplementation.signObject(assertion, SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA256,
                SignatureConstants.ALGO_ID_DIGEST_SHA256);

        return response;
    }

    private AuthnStatement getAuthnStatement(String msgId){
        AuthnStatement authnStatement = openSamlImplementation.buildSAMLObject(AuthnStatement.class);
        AuthnContext authnContext = openSamlImplementation.buildSAMLObject(AuthnContext.class);
        AuthnContextClassRef authnContextClassRef = openSamlImplementation.buildSAMLObject(AuthnContextClassRef.class);
        authnContextClassRef.setAuthnContextClassRef(AuthnContext.PASSWORD_AUTHN_CTX);
        authnContext.setAuthnContextClassRef(authnContextClassRef);
        authnStatement.setAuthnContext(authnContext);
        authnStatement.setAuthnInstant(new DateTime());
        //当从 SP 登出时 需要通过 SessionIndex 来确定出会话
        authnStatement.setSessionIndex(msgId);

        return authnStatement;
    }


    /**
     * HTTP POST BINDING 时用于编码返回结果并返回给浏览器
     * 使用其他方式返回时可以使用
     * {@link org.opensaml.saml.saml2.binding.encoding.impl.HTTPArtifactEncoder}
     * {@link org.opensaml.saml.saml2.binding.encoding.impl.HTTPRedirectDeflateEncoder}
     * {@link HTTPPostEncoder}
     * {@link org.opensaml.saml.saml2.binding.encoding.impl.HTTPSOAP11Encoder}
     * {@link org.opensaml.saml.saml2.binding.encoding.impl.HttpClientRequestSOAP11Encoder}
     * 等上类实现
     * @param relayState
     * @param res
     * @param acsUrl
     * @param response
     * @throws ComponentInitializationException
     * @throws MessageEncodingException
     */
    private void httpPostBinding(String relayState,
                                 HttpServletResponse res, String acsUrl, Response response)
            throws ComponentInitializationException, MessageEncodingException, MarshallingException {

        MessageContext messageContext = new MessageContext();
        messageContext.setMessage(response);
        if(hasText(relayState)) {
            messageContext.getSubcontext(SAMLBindingContext.class,true).setRelayState(relayState);
        }
        SAMLEndpointContext samlEndpointContext = messageContext.getSubcontext(SAMLPeerEntityContext.class,true).getSubcontext(SAMLEndpointContext.class,true);
        Endpoint endpoint = openSamlImplementation.buildSAMLObject(AssertionConsumerService.class);
        endpoint.setLocation(acsUrl);
        samlEndpointContext.setEndpoint(endpoint);

        log.debug("SAMLResponse xml:\n{}",openSamlImplementation.transformSAMLObject2String(response));

        //openSamlImplementation.
        HTTPPostEncoder httpPostEncoder = new HTTPPostEncoder();
        httpPostEncoder.setMessageContext(messageContext);
        httpPostEncoder.setVelocityEngine(velocityEngine);
        httpPostEncoder.setVelocityTemplateId("/templates/saml2-post-binding.vm");
        httpPostEncoder.setHttpServletResponse(res);
        httpPostEncoder.initialize();
        httpPostEncoder.encode();
    }


    public Issuer idpBuildIssuer() {
        Issuer issuer = openSamlImplementation.buildSAMLObject(Issuer.class);
        String idpEntityId = ConfigCenter.IDP_ENTITY_ID;
        issuer.setValue(idpEntityId);
        return issuer;
    }



    /**
     * 注意, 生成元数据的时候,使 EntityID 字段与 Issuer 里面的值一致,否则会有问题!
     * @return
     * @throws MarshallingException
     * @throws SignatureException
     * @throws java.lang.SecurityException
     */
    @RequestMapping("/metadata")
    @ResponseBody
    public ResponseEntity<ByteArrayResource> metadata()
            throws MarshallingException, SignatureException, SecurityException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", "attachment; filename=" + System.currentTimeMillis() + ".xml");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        headers.add("Last-Modified", new Date().toString());
        headers.add("ETag", String.valueOf(System.currentTimeMillis()));

        return ResponseEntity
                .ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new ByteArrayResource(this.generateIDPMetadataXML().getBytes()));
    }

    public String generateIDPMetadataXML() throws MarshallingException, SignatureException, SecurityException {
        EntityDescriptor entityDescriptor = openSamlImplementation.buildSAMLObject(EntityDescriptor.class);
        //EntityId是metadata地址
        String idpEntityId = ConfigCenter.IDP_ENTITY_ID;
        entityDescriptor.setEntityID(idpEntityId);
        //IDP用于SSO的描述符
        IDPSSODescriptor idpssoDescriptor = openSamlImplementation.buildSAMLObject(IDPSSODescriptor.class);
        //必须的
        idpssoDescriptor.addSupportedProtocol(SAMLConstants.SAML20P_NS);
        //不加签
        idpssoDescriptor.setWantAuthnRequestsSigned(false);
        //用于验断言的 Key 信息生成
        KeyDescriptor keyDescriptor = openSamlImplementation.buildSAMLObject(KeyDescriptor.class);
        KeyInfoGenerator keyInfoGenerator = openSamlImplementation.getKeyInfoGenerator(openSamlImplementation.getSelfCredential());
        keyDescriptor.setUse(UsageType.SIGNING);
        keyDescriptor.setKeyInfo(keyInfoGenerator.generate(openSamlImplementation.getSelfCredential()));
        idpssoDescriptor.getKeyDescriptors().add(keyDescriptor);
        //IDP返回的NameIDFormat
        NameIDFormat nameIDFormat = openSamlImplementation.buildSAMLObject(NameIDFormat.class);
        nameIDFormat.setFormat(NameIDType.UNSPECIFIED);
        idpssoDescriptor.getNameIDFormats().add(nameIDFormat);
        //SSO地址相关
        SingleSignOnService singleSignOnService = openSamlImplementation.buildSAMLObject(SingleSignOnService.class);
        singleSignOnService.setBinding(SAML2_POST_BINDING_URI);
        //本次接入这个URL不需要使用
        singleSignOnService.setLocation(ConfigCenter.IDP_ENTITY_ID);

        idpssoDescriptor.getSingleSignOnServices().add(singleSignOnService);
        entityDescriptor.getRoleDescriptors().add(idpssoDescriptor);

        return openSamlImplementation.transformSAMLObject2String(entityDescriptor);
    }




}
