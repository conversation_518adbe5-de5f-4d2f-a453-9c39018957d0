package com.fxiaoke.open.oasyncdata.util;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;

public class ContactsUtils {
    public static EmployeeDto getEmployeeInfo(String ei,
                                              String userId,
                                              EmployeeProviderService employeeProviderService) {
        try {
            GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
            arg.setEnterpriseId(Integer.valueOf(ei));
            arg.setEmployeeId(Integer.valueOf(userId));
            GetEmployeeDtoResult result = employeeProviderService.getEmployeeDto(arg);
            return result.getEmployeeDto();
        } catch (Exception e) {

        }
        return null;
    }

    public static DepartmentDto getDepInfo(String ei,
                                           String depId,
                                           DepartmentProviderService departmentProviderService) {
        try {
            GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
            arg.setEnterpriseId(Integer.valueOf(ei));
            arg.setDepartmentId(Integer.valueOf(depId));
            GetDepartmentDtoResult result = departmentProviderService.getDepartmentDto(arg);
            return result.getDepartment();
        } catch (Exception e) {

        }
        return null;
    }
}
