package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/1/5 11:45
 * 刷数据
 * @desc
 */
@Component("oABrushDataManger")
@Slf4j
public class OABrushDataManger {
    @Autowired
    private OASyncApiDao oaSyncApiDao;
    @Autowired
    private OAConnectInfoDao connectInfoDao;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private OASettingsDao oaSettingsDao;

    public Result<Void> fillDataCenterIdApi(String tenantId){
        if(StringUtils.isNotBlank(tenantId)){
              extracted(tenantId);
            return Result.newSuccess();
        }
        List<String> connectString = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
        for (String tenantIdItem : connectString) {
            extracted(tenantIdItem);
        }
        return Result.newSuccess();

    }

    private void extracted(String tenantId) {
        //api补充
        List<OAConnectInfoEntity> oaConnectInfoEntities = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listInfo(tenantId);
        String dataCenterId=oaConnectInfoEntities.get(0).getId();
        List<OASyncApiEntity> oaUsedSyncApiList = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAUsedSyncApiListIgnoreStatus(tenantId);
        List<String> apiList = oaUsedSyncApiList.stream().filter(item -> ObjectUtils.isEmpty(item.getDataCenterId())).map(OASyncApiEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(apiList)){
            Integer integer = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).fillDataCenterId(tenantId, dataCenterId, apiList);
        }
        log.info("fill data api:{}", tenantId);
        //先找出重复的erp_data_id的数据。进行删除。再刷数据
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities1 = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).queryDuplicateData(tenantId, ErpFieldTypeEnum.employee_oa);
        Map<String,ErpFieldDataMappingEntity> uniqueEntity= Maps.newHashMap();
        List<String> needRemoveId=Lists.newArrayList();
        for (ErpFieldDataMappingEntity erpFieldDataMappingEntity : erpFieldDataMappingEntities1) {
           if(ObjectUtils.isEmpty(uniqueEntity.get(erpFieldDataMappingEntity.getErpDataId()))){
               uniqueEntity.put(erpFieldDataMappingEntity.getErpDataId(),erpFieldDataMappingEntity);
           }else {
               needRemoveId.add(erpFieldDataMappingEntity.getId());
           }
        }
        log.info("need remove id :{}", JSONObject.toJSONString(needRemoveId));
        if(CollectionUtils.isNotEmpty(needRemoveId)){
            int dataIndex = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).batchDeleteByIds(tenantId, needRemoveId);
        }

//        //还有没有重复的数据，但是datacenter是空的
//        erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).queryDuplicateData()


        //账号刷数据中心id
        int limit=1000;
        int offset=0;

        while (true){
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listByTenantIdAndDataTypeNoDataCenterId(tenantId, ErpFieldTypeEnum.employee_oa, limit, offset, null);
            if(CollectionUtils.isEmpty(erpFieldDataMappingEntities)){
                break;
            }
            try {
                List<String> collect = erpFieldDataMappingEntities.stream().map(ErpFieldDataMappingEntity::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    int index = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).batchUpdateDataCenterId(tenantId,dataCenterId, collect);
                }
            } catch (Exception e) {
              log.info("message :{}",e.getMessage());
            }
            offset+=limit;
        }

        log.info("fill ErpFieldDataMappingEntity api:{}", tenantId);
        //刷日志配置
        try {
            List<OASettingDoc> listSetting = oaSettingsDao.getListSetting(tenantId);
            if(CollectionUtils.isNotEmpty(listSetting)){
                listSetting.stream().forEach(item ->item.setDataCenterId(dataCenterId));
                oaSettingsDao.batchUpdateById(tenantId,listSetting);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage()+"tenantId"+ tenantId);
        }
        log.info("fill oaSettingsDao api:{}", tenantId);
    }
}
