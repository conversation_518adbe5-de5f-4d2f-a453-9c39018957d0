package com.fxiaoke.open.oasyncdata.controller.oa;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;

import com.fxiaoke.open.oasyncdata.model.OAFlowMqConfigResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAFlowMqConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "OA流程配置相关接口")
@RestController("oAFlowMqConfigController")
@RequestMapping("cep/oa/oAFlowMqConfig")
@Slf4j
public class OAFlowMqConfigController extends BaseController {

    @Autowired
    OAFlowMqConfigService oaFlowMqConfigService;

    @ApiOperation(value = "新增或更新")
    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    public Result<List> addOrUpdate(@RequestBody List<OAFlowMqConfigResult> oaFlowMqConfigEntities,
                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {

        String tenantId = getLoginUserTenantId();
        return oaFlowMqConfigService.addOrUpdate(tenantId, oaFlowMqConfigEntities,lang);
    }

    @ApiOperation(value = "删除")
    @RequestMapping(value = "/deleteById", method = RequestMethod.POST)
    public Result<String> deleteById(@RequestBody Map params,
                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {

        String tenantId = getLoginUserTenantId();
        return oaFlowMqConfigService.deleteById(tenantId, params,lang);
    }

    @ApiOperation(value = "批量获取")
    @RequestMapping(value = "/list")
    public Result<List<OAFlowMqConfigResult>> list(@RequestBody CepArg cepArg) {

        String tenantId = getLoginUserTenantId();
        return oaFlowMqConfigService.list(tenantId);
    }
}
