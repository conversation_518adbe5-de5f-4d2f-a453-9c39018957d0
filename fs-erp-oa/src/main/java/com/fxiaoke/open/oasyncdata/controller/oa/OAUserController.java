package com.fxiaoke.open.oasyncdata.controller.oa;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.MultiLevelCache;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.AutoBindArg;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.arg.SyncRuleArg;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.ParallelUtils;
import com.fxiaoke.open.oasyncdata.model.IdListArg;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.model.QueryOAEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.OAFieldItemResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OAUserService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA用户相关接口")
@RestController("oaUserController")
@RequestMapping("cep/oa/user")
@Slf4j
public class OAUserController extends BaseController {
    @Autowired
    private OAUserService oaUserService;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CacheManager cacheManager;

    @ApiOperation(value = "获取用户列表")
    @RequestMapping(value = "/getOAUserInfo", method = RequestMethod.POST)
    public Result<QueryResult<List<EmployeeMappingResult>>> getOAUserInfo(@RequestBody QueryOAEmployeeMappingListArg queryArg) {
        String tenantId = getLoginUserTenantId();
        queryArg.setCurrentDcId(getDcId());
        return oaUserService.getOAUserList(tenantId, queryArg);
    }

    @ApiOperation(value = "更新或者新增用户绑定")
    @RequestMapping(value = "/updateOAUserInfo", method = RequestMethod.POST)
    public Result<String> updateOAUserInfo(@RequestBody EmployeeMappingResult employeeMappingResult) {
        String tenantId = getLoginUserTenantId();
        employeeMappingResult.setCurrentDcId(getDcId());
        if (employeeMappingResult.getFsEmployeeId() == null || employeeMappingResult.getErpEmployeeId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return oaUserService.updateOAUserInfo(tenantId, employeeMappingResult);
    }

    @ApiOperation(value = "删除用户绑定")
    @RequestMapping(value = "/deleteOAUserInfo", method = RequestMethod.POST)
    public Result<String> deleteOAUserInfo(@RequestBody EmployeeMappingResult employeeMappingResult,
                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        if (employeeMappingResult.getId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        //return oaUserService.deleteOAUserInfo(tenantId, employeeMappingResult);
        return oaUserService.batchDeleteOAUserMapping(tenantId,
                getDcId(),
                Lists.newArrayList(employeeMappingResult.getId()),
                lang);
    }

    @ApiOperation(value = "批量删除OA用户绑定数据")
    @RequestMapping(value = "/batchDeleteOAUserMapping", method = RequestMethod.POST)
    public Result<String> batchDeleteOAUserMapping(@RequestBody IdListArg deleteArg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return oaUserService.batchDeleteOAUserMapping(tenantId,
                getDcId(),
                deleteArg.getIdList(),
                lang);
    }

    @ApiOperation(value = "导出OA员工映射数据")
    @RequestMapping(value = "/exportOAUserMappingData", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> asyncExportOAUserMappingData(@RequestBody IdListArg arg,
                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) throws IOException {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();

        DeferredResult<Result<Void>> result = new DeferredResult<>(ConfigCenter.EXPORT_TIME_OUT,
                Result.newSuccess(i18NStringManager.get(I18NStringEnum.s103, lang, tenantId)));

        ParallelUtils.createBackgroundTask().submit(() -> {
            Result<Void> userMapping = oaUserService.exportOAUserMapping(tenantId, userId, dcId, arg.getIdList(), lang);
            result.setResult(userMapping);
        }).run();
        return result;
    }

    @ApiOperation(value = "设置绑定的规则")
    @RequestMapping(value = "/saveAutoBindRule", method = RequestMethod.POST)
    public Result<String> deleteOAUserInfo(@RequestBody AutoBindArg autoBindArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        OASettingVO oaSettingVO=new OASettingVO();
        oaSettingVO.setConfiguration(JSONObject.toJSONString(autoBindArg));
        oaSettingVO.setTenantId(tenantId);
        oaSettingVO.setCurrentDcId(dataCenterId);
        oaSettingVO.setType(OATenantEnum.OA_AUTO_BIND_FIELD.name());
        Result<String> settingInfo = oaSettingService.upsertSettingInfo(tenantId, oaSettingVO,dataCenterId);
        return settingInfo;
    }


    @ApiOperation(value = "返回设定的规则")
    @RequestMapping(value = "/returnUserRule", method = RequestMethod.POST)
    public Result<Object> returnUserRule(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_AUTO_BIND_FIELD,dataCenterId);
        return genericInfo;
    }

    @ApiOperation(value = "返回设定的规则")
    @RequestMapping(value = "/returnCache", method = RequestMethod.POST)
    public Result<Object> returnCache(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();

        MultiLevelCache<Object, Object> configTypeValue =
                (MultiLevelCache<Object, Object>) cacheManager.getCache("configTypeValue");
        String tenantIdkey=tenantId+OATenantEnum.OA_AUTO_BIND_FIELD.name();
        log.info("configTypeValue:{}", JSONObject.toJSONString(configTypeValue.get(tenantIdkey)));
        return Result.newSuccess(JSONObject.toJSONString(configTypeValue.get(tenantIdkey)));
    }

    @ApiOperation(value = "设置账号未绑定的时候，是否需要同步记录，标记无需同步，则不会重试")
    @RequestMapping(value = "/setSyncRule", method = RequestMethod.POST)
    public Result<String> setSyncRule(@RequestBody SyncRuleArg syncRuleArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        OASettingVO oaSettingVO=new OASettingVO();
        oaSettingVO.setConfiguration(JSONObject.toJSONString(syncRuleArg));
        oaSettingVO.setTenantId(tenantId);
        oaSettingVO.setType(OATenantEnum.OA_RECORD_NOT_BIND_ACCOUNT.name());
        Result<String> settingInfo = oaSettingService.upsertSettingInfo(tenantId, oaSettingVO,dataCenterId);
        return settingInfo;
    }

    @ApiOperation(value = "返回同步的规则")
    @RequestMapping(value = "/returnSyncRule", method = RequestMethod.POST)
    public Result<Object> returnRule(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_RECORD_NOT_BIND_ACCOUNT,dataCenterId);
        return genericInfo;
    }

    @ApiOperation(value = "返回人员对象的字段")
    @RequestMapping(value = "/getEmpField", method = RequestMethod.POST)
    public Result<List<OAFieldItemResult>> getEmpField(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        headerObj.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantId));
        ObjectDescribe describeResult = objectDescribeService.getDescribe(headerObj, "PersonnelObj").getData().getDescribe();
        List<OAFieldItemResult> oaFieldItemResults= Lists.newArrayList();
        for (FieldDescribe objField : describeResult.getFields().values()) {
            OAFieldItemResult oaFieldItemResult=new OAFieldItemResult();
            oaFieldItemResult.setFieldApiName(objField.getApiName());
            oaFieldItemResult.setFieldLabel(objField.getLabel());
            oaFieldItemResults.add(oaFieldItemResult);
        }
        return Result.newSuccess(oaFieldItemResults);
    }



}









