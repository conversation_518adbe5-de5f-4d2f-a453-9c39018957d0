package com.fxiaoke.open.oasyncdata.util;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.oasyncdata.db.util.JacksonUtil;
import com.fxiaoke.open.oasyncdata.model.OADealWithMsgLog;
import com.fxiaoke.open.oasyncdata.model.OAInterfaceMonitorLog;
import com.fxiaoke.ps.ProtostuffUtil;
import com.github.trace.TraceContext;
import org.apache.commons.lang3.StringUtils;

public class BizLogUtils {


    public static void sendOAInterfaceMonitorLog(String tenantId,String url,String params,String headerMap,String requestMode,
                                                 String exceptionType,String exceptionMsg,String httpResponse,Integer status,Long callTime,Long returnTime) {
        try{
            OAInterfaceMonitorLog dto=OAInterfaceMonitorLog.builder()
                    .stamp(System.currentTimeMillis())
                    .tenantId(tenantId)
                    .traceId(TraceContext.get().getTraceId())
                    .url(url)
                    .params(JacksonUtil.toJson(params))
                    .header(JacksonUtil.toJson(headerMap))
                    .requestMode(requestMode)
                    .exceptionType(exceptionType)
                    .exceptionMsg(exceptionMsg)
                    .httpResponse(httpResponse)
                    .status(status)
                    .callTime(callTime)
                    .returnTime(returnTime)
                    .costTime(returnTime-callTime)
                    .build();
            BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dto));;
        }catch (Exception e){

        }
    }
    public static void sendOADealWithMsgLog(String tenantId,String mqType,String operateType) {
        try{
            if(StringUtils.isBlank(tenantId)){
                return;
            }
            OADealWithMsgLog dto= OADealWithMsgLog.builder()
                    .stamp(System.currentTimeMillis())
                    .tenantId(tenantId)
                    .traceId(TraceContext.get().getTraceId())
                    .mqType(mqType)
                    .operateType(operateType)
                    .build();
            BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dto));;
        }catch (Exception e){

        }
    }
}
