package com.fxiaoke.open.oasyncdata.job;


import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.service.OASyncLogService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:10 2022/6/9
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "oASyncDataHandler")
public class OAReSyncDataHandler extends IJobHandler {
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private OASyncLogService oaSyncLogService;

    private static ExecutorService  executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("OAReSyncDataJob-%d").build();
        executorService = new ThreadPoolExecutor(10, 30, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }


    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) {
        List<String> allTenantIds = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
//        List<String> tenantIds = allTenantIds.stream().distinct().collect(Collectors.toList());//去重

//        tenantIds.removeAll(ConfigCenter.NOT_RE_SYNC_TENANT);
        for (String tenantId : allTenantIds) {
            //每个企业在单独线程运行
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    TraceUtil.initTraceWithFormat();
                    log.info("OAReSyncDataJob task start,ei={}",tenantId);
                    oaSyncLogService.resyncSettingCondition(tenantId);
                    log.info("OAReSyncDataJob task end,ei={}",tenantId);
                    TraceUtil.removeTrace();
                }
            });
        }
    }


    private boolean matchSharding(int index, int total, String tenantId) {
        long longId = getLong(tenantId);
        return longId % total == index;
    }

    private long getLong(String tenantId) {
        long longId;
        try {
            longId = Long.parseLong(tenantId);
        } catch (NumberFormatException e) {
            log.warn("this tenantId is not long type,tenantId:{}", tenantId, e);
            longId = tenantId.hashCode();
        }
        return longId;
    }

}
