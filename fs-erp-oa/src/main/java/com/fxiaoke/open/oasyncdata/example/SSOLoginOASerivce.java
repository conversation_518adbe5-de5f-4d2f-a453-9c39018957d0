package com.fxiaoke.open.oasyncdata.example;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.oasyncdata.manager.ProxyHttpClient;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/10 14:50 通过open api免登录纷享代办页面
 * @Version 1.0
 */
@Service
@Slf4j
public class SSOLoginOASerivce {

    public static String domainUrl = "https://open.ceshi112.com%s";
    public static String GET_ACCESS_TOKEN_UTL = "/cgi/corpAccessToken/get/V2";
    public static String SSO_LOGIN_URL = "/cgi/sso/loginurl/get";
    public static String APP_ID = "FSAID_13147c8";
    public static String APP_SECRET = "ea1fab3238484a918720680868657e5f";
    public static String PERMANENT_CODE = "8865799413C5221F03E665401C466A0F";
    public static String NONCE = "MT1ZFIFlPVSDmiJH";//自定义 ，一分钟内不要重复

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    /**
     * 建议缓存 expireTime=7200s
     *
     * @return
     */
    public Map<String, String> getAccessToken() {

        String finalAccessTokenUrl = String.format(domainUrl, GET_ACCESS_TOKEN_UTL);
        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("appId", APP_ID);
        paramsMap.put("appSecret", APP_SECRET);
        paramsMap.put("permanentCode", PERMANENT_CODE);
        String result = proxyHttpClient.postUrl(finalAccessTokenUrl, paramsMap, Maps.newHashMap());
        Map<String, String> resultMap = JSONObject.parseObject(result, new TypeReference<Map>() {
        });
        return resultMap;
    }

    /**
     * 通过openapi获取免登token
     * @param account 三种传值方式
     *account的类型，当前支持三种类型:
     * 1 代表手机号
     * 2 代表纷享登录账号
     * 3 代表OpenUserId
     */
    public String skipLogin(String accessToken, String corpId, String account) {
        String finalLoginUrl = String.format(domainUrl, SSO_LOGIN_URL);
        Long timeStamp = System.currentTimeMillis();
        Map<String, Object> paramsMap = Maps.newHashMap();
        //生成鉴权
        String signature = generateSignature(accessToken,corpId, timeStamp.toString(), NONCE, account.toString(), String.valueOf(2), APP_SECRET);
        paramsMap.put("corpAccessToken", accessToken);
        paramsMap.put("corpId", corpId);
        paramsMap.put("timestamp", timeStamp);
        paramsMap.put("nonce", NONCE);
        paramsMap.put("account", account);
        paramsMap.put("type", 2);
        paramsMap.put("signature", signature);
        String result = proxyHttpClient.postUrl(finalLoginUrl, paramsMap, Maps.newHashMap());
        Map<String, String> resultMap = JSONObject.parseObject(result, new TypeReference<Map>() {
        });
        return resultMap.get("loginUrl");
    }

    /**
     * 生成signature
     * 签名字符串，用来防篡改。签名规则为：
     * 1.首先将corpAccessToken、corpId、timestamp、nonce、account、type字段值以及appSecret（自建应用-开发者模式中可以查看）按
     * 照字符串值进行自然序排序。
     * 2.将排序后的字符串进行拼接。
     * 3.将拼接后的字符串进行SHA1计算进行16进制编码后获取签名。
     * @param  account 需要OA系统传递当前点击的用户在纷享的手机号码
     */
    public String generateSignature(String corpAccessToken, String corpId,String timestamp, String nonce, String account, String type, String appSecret) {
        List<String> params = Lists.newArrayList(corpAccessToken,corpId, timestamp, nonce, account, type, appSecret);

//
        Collections.sort(params);
        String result = DigestUtils.shaHex(Joiner.on(StringUtils.EMPTY).join(params));
        return result;
    }


    /**
     * 拼接详情的免登地址
     */
    public String concatDetailUrl(String tokenUrl,String crmDetailUrl)  {
        StringBuilder redirectUrl= null;
        try {
            redirectUrl = new StringBuilder().append(tokenUrl).append("&source=").append(URLEncoder.encode(crmDetailUrl,"UTF-8"));
            return redirectUrl.toString();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }




}

