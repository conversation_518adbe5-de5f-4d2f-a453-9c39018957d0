package com.fxiaoke.open.erpsyncdata.preprocess.remote

import com.facishare.converter.impl.EIEAConverterImpl
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore

import java.util.function.Function
import java.util.stream.Collectors
import java.util.stream.IntStream

/**
 * <AUTHOR> 
 * @date 2024/4/10 10:01:55
 */
@Ignore
@ContextConfiguration("classpath:test/ei-ea-test.xml")
class EIEAConverterTest extends BaseSpockTest {

    @Autowired
    private EIEAConverterImpl eiEaConverter

    def "缓存虚假企业数据"() {
        expect:
        def collect = IntStream.range(500_0000, 500_5001).boxed().collect(Collectors.toMap(Function.identity(), { String.valueOf(it) }))
        eiEaConverter.eieaOperator.save(collect)
    }


    def "id"() {
        expect:
        def collect = IntStream.range(500_0000, 500_5001).boxed()
                .filter { it % 2 == 0 }
                .map { String.valueOf(it) }
                .collect(Collectors.joining("|"))
        println collect
        def collect2 = IntStream.range(500_0000, 500_5001).boxed()
                .filter { it % 2 == 1 }
                .map { String.valueOf(it) }
                .collect(Collectors.joining("|"))
        println collect2
    }
}
