package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/6 11:48:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelationErpShardDto implements Serializable {
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 上游企业id
     */
    @TenantID
    private String tenantId;
    /**
     * 模板企业id
     */
    private String templateId;
    /**
     * 模板企业数据中心id
     */
    private String dcId;
    /**
     * 下游企业id,同时也是代管企业Id
     */
    private String downstreamId;
    /**
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum
     */
    private Integer status;

}
